# 肺功能数据管理平台

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Node](https://img.shields.io/badge/node-%3E%3D18.0.0-green.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

一个现代化的全栈数据管理平台，专为处理金数据表单提交、用户管理和数据分析而设计。

[功能特点](#功能特点) •
[快速开始](#快速开始) •
[部署指南](#部署指南) •
[API文档](#api文档) •
[贡献指南](#贡献指南)

</div>

## 📋 目录

- [功能特点](#功能特点)
- [技术栈](#技术栈)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [部署指南](#部署指南)
- [API文档](#api文档)
- [目录结构](#目录结构)
- [开发指南](#开发指南)
- [性能优化](#性能优化)
- [安全性](#安全性)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## ✨ 功能特点

### 🎯 核心功能
- **表单配置管理**: 可视化配置金数据表单字段映射
- **Webhook接收**: 自动接收和处理金数据推送
- **动态表创建**: 根据配置自动创建数据库表结构
- **数据管理**: 强大的数据查询、编辑、批量操作功能
- **用户权限**: 完整的用户管理和角色权限控制

### 📊 数据处理
- **高级搜索**: 多字段组合搜索、日期范围筛选
- **批量操作**: 批量删除、批量更新数据
- **数据导出**: Excel/CSV格式导出，支持自定义字段
- **实时统计**: 数据量统计、接收状态监控

### 🛡️ 安全特性
- **身份认证**: NextAuth.js驱动的安全认证系统
- **密码加密**: bcrypt加密存储，支持密码强度检测
- **操作审计**: 完整的系统操作日志记录
- **权限控制**: 基于角色的访问控制(RBAC)

### 🎨 用户体验
- **响应式设计**: 完美适配桌面、平板、移动设备
- **现代UI**: 基于Ant Design的美观界面
- **实时反馈**: 操作状态实时提示和进度显示
- **个性化**: 头像上传、个人信息管理

## 🛠️ 技术栈

### 前端技术
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **UI库**: Ant Design 5.x
- **样式**: Tailwind CSS
- **状态管理**: React Hooks + Context
- **日期处理**: dayjs
- **图表**: 内置统计图表

### 后端技术
- **运行时**: Node.js 18+
- **框架**: Next.js API Routes
- **数据库**: MySQL 8.0 (Prisma ORM)
- **认证**: NextAuth.js
- **文件上传**: 本地存储
- **缓存**: 内存缓存系统

### 部署运维
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **SSL**: Let's Encrypt
- **监控**: 健康检查 + 日志系统
- **数据库**: 腾讯云轻量数据库

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   金数据平台      │───▶│  Webhook接收      │───▶│   数据处理引擎    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面       │◀───│  Next.js应用     │◀───│   MySQL数据库    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                    ┌─────────────────┐
                    │   缓存系统       │
                    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- MySQL 8.0+
- npm 或 yarn

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd free_lung_function_project_admin
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
```

编辑 `.env.local`:
```env
DATABASE_URL="mysql://username:password@localhost:3306/database"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"
```

4. **初始化数据库**
```bash
npx prisma db push
```

5. **启动开发服务器**
```bash
npm run dev
```

6. **访问应用**
打开 [http://localhost:3000](http://localhost:3000)

默认管理员账户: `admin` / `admin123`

## 🐳 部署指南

### Docker 快速部署

1. **使用部署脚本**
```bash
# 克隆项目
git clone <repository-url>
cd free_lung_function_project_admin

# 配置环境变量
cp .env.example .env.production
# 编辑 .env.production

# 执行部署
./scripts/deploy.sh
```

2. **手动部署**
```bash
# 构建并启动
docker-compose up -d

# 初始化数据库
docker-compose exec app npx prisma db push
```

### 生产环境部署

详细部署指南请参考 [DEPLOYMENT.md](docs/DEPLOYMENT.md)

## 📚 API文档

完整的API文档请参考 [API.md](docs/API.md)

### 主要端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/forms` | GET/POST | 表单配置管理 |
| `/api/data/{formId}` | GET | 获取表单数据 |
| `/api/webhook/{formId}` | POST | 接收Webhook推送 |
| `/api/users` | GET/POST | 用户管理 |
| `/api/dashboard` | GET | 仪表板数据 |
| `/api/health` | GET | 健康检查 |

## 📁 目录结构

```
free_lung_function_project_admin/
├── src/                          # 源代码
│   ├── app/                      # Next.js App Router
│   │   ├── (dashboard)/          # 仪表板页面
│   │   ├── api/                  # API路由
│   │   └── auth/                 # 认证页面
│   ├── components/               # React组件
│   │   ├── forms/                # 表单组件
│   │   ├── layout/               # 布局组件
│   │   └── ui/                   # UI组件
│   ├── lib/                      # 工具库
│   │   ├── auth.ts               # 认证配置
│   │   ├── prisma.ts             # 数据库连接
│   │   ├── cache.ts              # 缓存系统
│   │   └── dynamicTable.ts       # 动态表管理
│   └── types/                    # TypeScript类型定义
├── prisma/                       # 数据库schema
├── docker/                       # Docker配置
├── docs/                         # 文档
├── scripts/                      # 部署脚本
├── public/                       # 静态资源
└── uploads/                      # 上传文件
```

## 🔧 开发指南

### 开发环境配置

1. **推荐IDE**: VS Code
2. **必需插件**:
   - ESLint
   - Prettier
   - Tailwind CSS IntelliSense
   - Prisma

### 代码规范

- **TypeScript**: 严格模式
- **ESLint**: 标准配置 + React规则
- **Prettier**: 代码格式化
- **Commit**: 语义化提交信息

### 开发流程

1. 创建功能分支
2. 开发和测试
3. 提交代码
4. 创建Pull Request
5. 代码审查
6. 合并到主分支

## ⚡ 性能优化

### 已实现的优化

- **数据库索引**: 针对查询优化的复合索引
- **内存缓存**: API响应缓存和会话缓存
- **代码分割**: 自动代码分割和懒加载
- **图片优化**: WebP格式支持和压缩
- **静态资源**: 长期缓存策略
- **Gzip压缩**: 响应数据压缩

### 性能监控

- 健康检查端点
- 内存使用监控
- 数据库性能监控
- API响应时间跟踪

## 🔒 安全性

### 安全措施

- **密码安全**: bcrypt加密 + 强度检测
- **会话管理**: JWT + 安全cookie
- **CSRF保护**: 内置CSRF防护
- **XSS防护**: 输入验证和输出转义
- **SQL注入**: Prisma ORM参数化查询
- **文件上传**: 类型检查和大小限制

### 安全头设置

- Strict-Transport-Security
- X-Content-Type-Options
- X-Frame-Options
- X-XSS-Protection
- Content-Security-Policy

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 贡献类型

- 🐛 Bug修复
- ✨ 新功能
- 📝 文档改进
- 🎨 UI/UX改进
- ⚡ 性能优化
- 🔒 安全增强

## 📈 开发状态

### 已完成功能 ✅

- [x] 用户认证和授权
- [x] 表单配置管理
- [x] Webhook数据接收
- [x] 动态表创建
- [x] 数据查询和管理
- [x] 批量操作功能
- [x] 数据导出功能
- [x] 系统日志审计
- [x] 响应式UI设计
- [x] Docker部署配置
- [x] 性能优化
- [x] 安全加固

### 计划功能 🚧

- [ ] 数据可视化图表
- [ ] 邮件通知系统
- [ ] 多语言支持
- [ ] API限流优化
- [ ] 数据备份恢复

## 📊 项目统计

- **代码行数**: ~15,000+ 行
- **组件数量**: 25+ 个React组件
- **API端点**: 16+ 个RESTful接口
- **数据库表**: 3个核心表 + 动态表
- **功能页面**: 12+ 个功能页面

## 🎯 使用场景

### 适用于以下场景：

- 📋 **表单数据管理**: 收集和管理在线表单提交数据
- 🏥 **医疗预约系统**: 患者预约信息管理
- 📊 **数据分析平台**: 结构化数据处理和分析
- 🎓 **教育管理系统**: 学生信息和课程管理
- 🏢 **企业数据平台**: 内部数据收集和处理

## 🌟 核心亮点

1. **零配置部署**: Docker一键部署，开箱即用
2. **可视化配置**: 图形化界面配置表单字段映射
3. **实时处理**: Webhook实时接收和处理数据
4. **灵活扩展**: 支持任意表单结构和字段类型
5. **企业级安全**: 完整的安全防护和审计系统

## 📞 支持

如果您在使用过程中遇到问题：

1. 📖 查看[文档](docs/)
2. 🐛 提交[Issue](../../issues)
3. 💬 参与[讨论](../../discussions)
4. 📧 联系支持团队

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

<div align="center">

**肺功能数据管理平台** - 让数据管理更简单、更安全、more powerful！

[⬆ 回到顶部](#肺功能数据管理平台)

</div>