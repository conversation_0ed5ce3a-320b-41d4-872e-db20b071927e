'use client'

import { useState, useEffect } from 'react'
import { Card, Table, Button, Space, Tag, message, Modal } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import type { ColumnsType } from 'antd/es/table'

interface FormConfig {
  id: string
  formId: string
  formName: string
  isActive: boolean
  fieldCount: number
  webhookUrl: string
  createdAt: string
  updatedAt: string
}

export default function FormsListPage() {
  const [loading, setLoading] = useState(true)
  const [forms, setForms] = useState<FormConfig[]>([])
  const router = useRouter()

  useEffect(() => {
    fetchForms()
  }, [])

  const fetchForms = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/forms')
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data && Array.isArray(result.data.forms)) {
          setForms(result.data.forms)
        } else {
          console.error('API返回数据格式错误:', result)
          setForms([]) // 确保设置为空数组而不是undefined
          message.error('获取表单列表失败：数据格式错误')
        }
      } else {
        console.error('API请求失败:', response.status, response.statusText)
        setForms([]) // 确保设置为空数组而不是undefined
        message.error('获取表单列表失败')
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      setForms([]) // 确保设置为空数组而不是undefined
      message.error('获取表单列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = (formId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除表单配置将同时删除所有相关数据，此操作不可恢复。确定要删除吗？',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await fetch(`/api/forms/${formId}`, {
            method: 'DELETE',
          })
          if (response.ok) {
            message.success('删除成功')
            fetchForms()
          } else {
            message.error('删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      },
    })
  }

  const columns: ColumnsType<FormConfig> = [
    {
      title: '表单名称',
      dataIndex: 'formName',
      key: 'formName',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-gray-500 text-sm">ID: {record.formId}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '字段数量',
      dataIndex: 'fieldCount',
      key: 'fieldCount',
      render: (count) => (
        <span className="font-mono">{count?.toLocaleString() ?? '0'}</span>
      ),
    },
    {
      title: 'Webhook URL',
      dataIndex: 'webhookUrl',
      key: 'webhookUrl',
      render: (url) => (
        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
          {url ? url.substring(0, 50) + '...' : '未设置'}
        </code>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => router.push(`/data/view?formId=${record.formId}`)}
          >
            查看数据
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => router.push(`/forms/config?id=${record.formId}`)}
          >
            编辑
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">表单列表</h1>
          <p className="text-gray-600 mt-1">管理所有已配置的表单和其数据接收状态</p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => router.push('/forms/config')}
        >
          新建表单配置
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={Array.isArray(forms) ? forms : []}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个表单配置`,
          }}
        />
      </Card>
    </div>
  )
}