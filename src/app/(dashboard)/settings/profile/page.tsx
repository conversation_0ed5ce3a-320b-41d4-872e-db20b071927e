'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  Card,
  Typography,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  message,
  Divider,
  Space,
  Alert,
  Modal,
} from 'antd'
import {
  UserOutlined,
  UploadOutlined,
  SaveOutlined,
  EditOutlined,
  CameraOutlined,
} from '@ant-design/icons'
import type { UploadFile, UploadProps } from 'antd/es/upload/interface'

const { Title, Text } = Typography

interface UserProfile {
  id: string
  username: string
  nickname: string
  email: string
  avatar: string | null
  createdAt: string
  lastLoginAt: string | null
}

export default function ProfilePage() {
  const { data: session, update } = useSession()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [profileData, setProfileData] = useState<UserProfile | null>(null)
  const [avatarUrl, setAvatarUrl] = useState<string>('')
  const [avatarFile, setAvatarFile] = useState<UploadFile | null>(null)

  // 获取用户资料
  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      const result = await response.json()

      if (result.success) {
        setProfileData(result.data)
        setAvatarUrl(result.data.avatar || '')
        form.setFieldsValue({
          username: result.data.username,
          nickname: result.data.nickname,
          email: result.data.email,
        })
      } else {
        message.error('获取用户资料失败')
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
      message.error('获取用户资料失败')
    }
  }

  // 更新用户资料
  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      const formData = new FormData()
      formData.append('nickname', values.nickname)
      formData.append('email', values.email || '')

      // 如果有新头像，添加到表单数据
      if (avatarFile) {
        formData.append('avatar', avatarFile.originFileObj!)
      }

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        body: formData,
      })

      const result = await response.json()

      if (result.success) {
        message.success('个人资料更新成功')
        // 更新session数据
        await update({
          ...session,
          user: {
            ...session?.user,
            nickname: values.nickname,
            avatar: result.data.avatar,
          }
        })
        // 重新获取资料
        fetchProfile()
        setAvatarFile(null)
      } else {
        message.error(result.error || '更新失败')
      }
    } catch (error) {
      console.error('更新用户资料失败:', error)
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }

  // 头像上传配置
  const uploadProps: UploadProps = {
    name: 'avatar',
    listType: 'picture-card',
    showUploadList: false,
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!')
        return false
      }

      // 预览图片
      const reader = new FileReader()
      reader.onload = (e) => {
        setAvatarUrl(e.target?.result as string)
      }
      reader.readAsDataURL(file)

      setAvatarFile({
        uid: file.uid,
        name: file.name,
        status: 'done',
        originFileObj: file,
      } as UploadFile)

      return false // 阻止自动上传
    },
  }

  useEffect(() => {
    fetchProfile()
  }, [])

  if (!profileData) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text>加载中...</Text>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="mb-2">
          个人设置
        </Title>
        <Text type="secondary">
          管理您的个人资料和账户信息
        </Text>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧 - 头像和基本信息 */}
        <div className="lg:col-span-1">
          <Card title="个人头像" className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <Avatar
                  size={120}
                  src={avatarUrl}
                  icon={<UserOutlined />}
                  className="border-4 border-gray-100"
                />
                <Upload {...uploadProps}>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<CameraOutlined />}
                    size="small"
                    className="absolute -bottom-2 -right-2 shadow-lg"
                  />
                </Upload>
              </div>
              
              <div className="text-center">
                <Title level={4} className="mb-1">
                  {profileData.nickname || profileData.username}
                </Title>
                <Text type="secondary" className="block">
                  @{profileData.username}
                </Text>
                {profileData.email && (
                  <Text type="secondary" className="block text-sm mt-1">
                    {profileData.email}
                  </Text>
                )}
              </div>
            </div>

            <Divider />

            <div className="space-y-2 text-left">
              <div className="flex justify-between">
                <Text type="secondary">注册时间:</Text>
                <Text className="text-sm">
                  {new Date(profileData.createdAt).toLocaleDateString('zh-CN')}
                </Text>
              </div>
              {profileData.lastLoginAt && (
                <div className="flex justify-between">
                  <Text type="secondary">最后登录:</Text>
                  <Text className="text-sm">
                    {new Date(profileData.lastLoginAt).toLocaleString('zh-CN')}
                  </Text>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* 右侧 - 编辑表单 */}
        <div className="lg:col-span-2">
          <Card
            title={
              <div className="flex items-center space-x-2">
                <EditOutlined />
                <span>编辑资料</span>
              </div>
            }
          >
            <Alert
              message="个人信息设置"
              description="您可以修改昵称和邮箱地址。用户名为系统唯一标识，不可修改。"
              type="info"
              showIcon
              className="mb-6"
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              autoComplete="off"
            >
              <Form.Item
                label="用户名"
                name="username"
              >
                <Input
                  disabled
                  prefix={<UserOutlined />}
                  placeholder="系统唯一标识"
                />
              </Form.Item>

              <Form.Item
                label="昵称"
                name="nickname"
                rules={[
                  { required: true, message: '请输入昵称' },
                  { max: 50, message: '昵称不能超过50个字符' },
                  { min: 2, message: '昵称至少2个字符' },
                ]}
              >
                <Input
                  placeholder="请输入您的昵称"
                  maxLength={50}
                />
              </Form.Item>

              <Form.Item
                label="邮箱地址"
                name="email"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' },
                  { max: 100, message: '邮箱地址不能超过100个字符' },
                ]}
              >
                <Input
                  placeholder="请输入您的邮箱地址（可选）"
                  maxLength={100}
                />
              </Form.Item>

              <Divider />

              <Form.Item className="mb-0">
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                    size="large"
                  >
                    保存更改
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields()
                      setAvatarUrl(profileData.avatar || '')
                      setAvatarFile(null)
                    }}
                    disabled={loading}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </div>
      </div>
    </div>
  )
}