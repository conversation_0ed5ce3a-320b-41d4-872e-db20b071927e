'use client'

import { useState } from 'react'
import {
  Card,
  Typography,
  Form,
  Input,
  Button,
  Alert,
  Space,
  Progress,
  message,
} from 'antd'
import {
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  SaveOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography

interface PasswordStrength {
  score: number
  level: string
  color: string
  suggestions: string[]
}

export default function PasswordPage() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    level: '',
    color: '',
    suggestions: []
  })

  // 密码强度检测
  const checkPasswordStrength = (password: string): PasswordStrength => {
    let score = 0
    const suggestions: string[] = []

    if (!password) {
      return { score: 0, level: '', color: '', suggestions: [] }
    }

    // 长度检查
    if (password.length >= 8) {
      score += 25
    } else {
      suggestions.push('密码至少需要8个字符')
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 15
    } else {
      suggestions.push('包含小写字母')
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 15
    } else {
      suggestions.push('包含大写字母')
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 15
    } else {
      suggestions.push('包含数字')
    }

    // 包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 15
    } else {
      suggestions.push('包含特殊字符')
    }

    // 长度奖励
    if (password.length >= 12) {
      score += 10
    }

    // 无重复字符
    if (!/(.)\1{2,}/.test(password)) {
      score += 5
    } else {
      suggestions.push('避免连续重复字符')
    }

    // 确定强度等级
    let level = ''
    let color = ''

    if (score < 30) {
      level = '弱'
      color = '#ff4d4f'
    } else if (score < 60) {
      level = '中等'
      color = '#faad14'
    } else if (score < 80) {
      level = '强'
      color = '#52c41a'
    } else {
      level = '非常强'
      color = '#1890ff'
    }

    return { score: Math.min(score, 100), level, color, suggestions }
  }

  // 处理新密码输入
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setPasswordStrength(checkPasswordStrength(password))
  }

  // 提交密码修改
  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      const response = await fetch('/api/user/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: values.currentPassword,
          newPassword: values.newPassword,
        }),
      })

      const result = await response.json()

      if (result.success) {
        message.success('密码修改成功！')
        form.resetFields()
        setPasswordStrength({ score: 0, level: '', color: '', suggestions: [] })
      } else {
        message.error(result.error || '密码修改失败')
      }
    } catch (error) {
      console.error('密码修改失败:', error)
      message.error('密码修改失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="mb-2">
          修改密码
        </Title>
        <Text type="secondary">
          为了账户安全，请定期更换密码
        </Text>
      </div>

      <div className="max-w-2xl">
        <Card
          title={
            <div className="flex items-center space-x-2">
              <LockOutlined />
              <span>密码设置</span>
            </div>
          }
        >
          <Alert
            message="密码安全提醒"
            description={
              <div className="space-y-1">
                <div>• 密码长度至少8个字符，建议12个字符以上</div>
                <div>• 包含大小写字母、数字和特殊字符</div>
                <div>• 避免使用生日、姓名等个人信息</div>
                <div>• 定期更换密码，增强账户安全</div>
              </div>
            }
            type="info"
            showIcon
            className="mb-6"
          />

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            autoComplete="off"
          >
            <Form.Item
              label="当前密码"
              name="currentPassword"
              rules={[
                { required: true, message: '请输入当前密码' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入当前密码进行验证"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              label="新密码"
              name="newPassword"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 8, message: '密码至少8个字符' },
                { max: 50, message: '密码不能超过50个字符' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve()
                    const strength = checkPasswordStrength(value)
                    if (strength.score < 60) {
                      return Promise.reject(new Error('密码强度不够，请设置更复杂的密码'))
                    }
                    return Promise.resolve()
                  }
                }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入新密码"
                onChange={handlePasswordChange}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            {/* 密码强度指示器 */}
            {passwordStrength.score > 0 && (
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <Text strong>密码强度:</Text>
                  <Text style={{ color: passwordStrength.color }}>
                    {passwordStrength.level}
                  </Text>
                </div>
                <Progress
                  percent={passwordStrength.score}
                  strokeColor={passwordStrength.color}
                  showInfo={false}
                  size="small"
                />
                {passwordStrength.suggestions.length > 0 && (
                  <div className="mt-3">
                    <Text type="secondary" className="text-sm">
                      建议改进:
                    </Text>
                    <ul className="mt-1 space-y-1">
                      {passwordStrength.suggestions.map((suggestion, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-center">
                          <ExclamationCircleOutlined className="mr-1 text-orange-500" />
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            <Form.Item
              label="确认新密码"
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入新密码"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item className="mb-0">
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                  size="large"
                >
                  修改密码
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields()
                    setPasswordStrength({ score: 0, level: '', color: '', suggestions: [] })
                  }}
                  disabled={loading}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  )
}