'use client'

import { Card, Collapse, Typography, Steps, Alert, Tag } from 'antd'
import { 
  QuestionCircleOutlined, 
  FileTextOutlined, 
  DatabaseOutlined, 
  SettingOutlined,
  BulbOutlined,
  WarningOutlined
} from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography
const { Panel } = Collapse

export default function HelpPage() {
  const quickStartSteps = [
    {
      title: '创建表单配置',
      description: '在"表单管理 > 表单配置"中输入金数据表单的 JSON 示例，系统会自动解析字段结构。',
    },
    {
      title: '配置 Webhook',
      description: '将生成的 Webhook URL 配置到金数据表单的推送设置中。',
    },
    {
      title: '测试数据接收',
      description: '提交一条测试数据，确认数据能正常接收和存储。',
    },
    {
      title: '管理数据',
      description: '在"数据管理"中查看、编辑、导出和批量操作表单数据。',
    },
  ]

  const faqItems = [
    {
      key: '1',
      label: '如何配置新的表单？',
      children: (
        <div className="space-y-3">
          <p>1. 登录金数据，获取表单提交后的 JSON 数据示例</p>
          <p>2. 在系统中点击"表单管理 &gt; 表单配置"</p>
          <p>3. 粘贴 JSON 数据，系统会自动解析字段</p>
          <p>4. 确认字段映射关系，保存配置</p>
          <p>5. 将生成的 Webhook URL 配置到金数据表单中</p>
        </div>
      ),
    },
    {
      key: '2',
      label: '数据接收失败怎么办？',
      children: (
        <div className="space-y-3">
          <p>请检查以下几个方面：</p>
          <ul className="list-disc list-inside space-y-1">
            <li>金数据 Webhook URL 配置是否正确</li>
            <li>表单字段是否有变更（需要重新配置）</li>
            <li>网络连接是否正常</li>
            <li>查看"系统设置 &gt; 系统日志"了解具体错误</li>
          </ul>
        </div>
      ),
    },
    {
      key: '3',
      label: '如何导出数据？',
      children: (
        <div className="space-y-3">
          <p>1. 进入"数据管理 &gt; 数据导出"</p>
          <p>2. 选择要导出的表单</p>
          <p>3. 设置时间范围和导出格式</p>
          <p>4. 点击"开始导出"下载文件</p>
          <p>支持 Excel 和 CSV 两种格式，可以设置导出字段。</p>
        </div>
      ),
    },
    {
      key: '4',
      label: '批量操作如何使用？',
      children: (
        <div className="space-y-3">
          <p>批量导入：</p>
          <ul className="list-disc list-inside space-y-1">
            <li>下载对应表单的导入模板</li>
            <li>按照模板格式准备数据</li>
            <li>上传 Excel 或 CSV 文件</li>
          </ul>
          <p className="mt-3">批量删除：选择表单后可以清空所有数据</p>
        </div>
      ),
    },
    {
      key: '5',
      label: '用户权限如何管理？',
      children: (
        <div className="space-y-3">
          <p>系统支持多用户管理：</p>
          <ul className="list-disc list-inside space-y-1">
            <li>管理员可以在"系统设置 &gt; 用户管理"中添加用户</li>
            <li>支持设置不同的角色权限</li>
            <li>用户可以修改自己的密码和个人信息</li>
          </ul>
        </div>
      ),
    },
  ]

  const troubleshootingItems = [
    {
      title: '登录问题',
      description: '无法登录或忘记密码',
      solution: '联系管理员重置密码，或在登录页面使用"忘记密码"功能'
    },
    {
      title: '页面加载慢',
      description: '系统响应速度慢',
      solution: '检查网络连接，清理浏览器缓存，或联系技术支持'
    },
    {
      title: '数据同步延迟',
      description: '金数据提交后，系统中看不到数据',
      solution: '数据同步通常在几分钟内完成，如果长时间未同步请检查 Webhook 配置'
    },
    {
      title: '导出文件损坏',
      description: '下载的 Excel 文件无法打开',
      solution: '尝试重新导出，或联系技术支持检查服务器状态'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">帮助文档</h1>
        <p className="text-gray-600 mt-1">系统使用指南和常见问题解答</p>
      </div>

      <Alert
        message="快速提示"
        description="如果您是第一次使用，建议按照下面的快速开始指南进行操作。遇到问题时，可以查看常见问题或联系技术支持。"
        type="info"
        showIcon
        icon={<BulbOutlined />}
      />

      <Card title={<><SettingOutlined className="mr-2" />快速开始</>}>
        <Steps
          direction="vertical"
          items={quickStartSteps.map((step, index) => ({
            title: step.title,
            description: step.description,
            status: 'process'
          }))}
        />
      </Card>

      <Card title={<><QuestionCircleOutlined className="mr-2" />常见问题</>}>
        <Collapse items={faqItems} />
      </Card>

      <Card title={<><FileTextOutlined className="mr-2" />功能说明</>}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div>
              <Title level={4}>表单管理</Title>
              <Paragraph>
                <Text strong>表单配置：</Text>创建和配置金数据表单的字段映射关系
              </Paragraph>
              <Paragraph>
                <Text strong>表单列表：</Text>查看所有已配置的表单状态和数据统计
              </Paragraph>
            </div>
            
            <div>
              <Title level={4}>数据管理</Title>
              <Paragraph>
                <Text strong>数据查看：</Text>浏览和搜索表单提交的数据
              </Paragraph>
              <Paragraph>
                <Text strong>数据导出：</Text>将数据导出为 Excel 或 CSV 格式
              </Paragraph>
              <Paragraph>
                <Text strong>批量操作：</Text>批量导入、更新或删除数据
              </Paragraph>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <Title level={4}>系统设置</Title>
              <Paragraph>
                <Text strong>个人设置：</Text>修改个人信息和偏好设置
              </Paragraph>
              <Paragraph>
                <Text strong>修改密码：</Text>更新账户密码
              </Paragraph>
              <Paragraph>
                <Text strong>用户管理：</Text>管理系统用户和权限
              </Paragraph>
              <Paragraph>
                <Text strong>系统日志：</Text>查看系统操作和错误日志
              </Paragraph>
            </div>
          </div>
        </div>
      </Card>

      <Card title={<><WarningOutlined className="mr-2" />故障排除</>}>
        <div className="space-y-4">
          {troubleshootingItems.map((item, index) => (
            <div key={index} className="border-l-4 border-blue-500 pl-4">
              <Title level={5} className="!mb-1">{item.title}</Title>
              <Paragraph className="text-gray-600 !mb-1">{item.description}</Paragraph>
              <Paragraph><Text strong>解决方案：</Text>{item.solution}</Paragraph>
            </div>
          ))}
        </div>
      </Card>

      <Card title={<><DatabaseOutlined className="mr-2" />技术规格</>}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Title level={4}>支持的数据格式</Title>
            <div className="space-y-2">
              <div><Tag color="blue">JSON</Tag> 金数据 Webhook 数据</div>
              <div><Tag color="green">Excel</Tag> .xlsx, .xls 文件</div>
              <div><Tag color="orange">CSV</Tag> 逗号分隔值文件</div>
            </div>
          </div>
          
          <div>
            <Title level={4}>系统限制</Title>
            <ul className="space-y-1">
              <li>单次导入文件大小：最大 10MB</li>
              <li>单次导入记录数：最大 10,000 条</li>
              <li>导出记录数：最大 100,000 条</li>
              <li>并发用户数：最大 50 个</li>
            </ul>
          </div>
        </div>
      </Card>

      <Card>
        <div className="text-center space-y-2">
          <Title level={4}>需要更多帮助？</Title>
          <Paragraph>
            如果以上信息无法解决您的问题，请联系技术支持：
          </Paragraph>
          <div className="space-x-4">
            <Text strong>邮箱：</Text>
            <Text code><EMAIL></Text>
          </div>
          <div className="space-x-4">
            <Text strong>电话：</Text>
            <Text code>400-xxx-xxxx</Text>
          </div>
        </div>
      </Card>
    </div>
  )
}