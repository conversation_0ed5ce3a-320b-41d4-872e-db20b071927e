'use client'

import { Layout } from 'antd'
import { useState, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { PageLoading } from '@/components/ui/Loading'

const { Content } = Layout

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [collapsed, setCollapsed] = useState(false)
  const { data: session, status } = useSession()
  const router = useRouter()

  // 检查认证状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // 处理退出登录
  const handleLogout = async () => {
    try {
      await signOut({
        redirect: false,
      })
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }

  // 显示加载状态
  if (status === 'loading') {
    return <PageLoading text="正在验证用户身份..." />
  }

  // 未认证时显示加载页面（即将重定向）
  if (status === 'unauthenticated') {
    return <PageLoading text="正在跳转到登录页..." />
  }

  // 已认证，显示仪表板
  return (
    <Layout className="min-h-screen">
      {/* 侧边栏 */}
      <Sidebar collapsed={collapsed} onCollapse={setCollapsed} />

      {/* 右侧主要内容区域 */}
      <Layout>
        {/* 顶部导航 */}
        <Header user={session?.user} onLogout={handleLogout} />

        {/* 主内容区 */}
        <Content className="m-6 p-6 bg-white rounded-lg shadow-ant min-h-[calc(100vh-180px)] fade-in">
          {children}
        </Content>

        {/* 底部 */}
        <Footer showFullFooter={false} className="text-center py-4" />
      </Layout>
    </Layout>
  )
}