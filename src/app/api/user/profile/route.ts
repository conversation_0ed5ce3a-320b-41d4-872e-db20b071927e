import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { z } from 'zod'

// 用户资料更新验证schema
const updateProfileSchema = z.object({
  nickname: z.string().min(2, '昵称至少2个字符').max(50, '昵称不能超过50个字符'),
  email: z.string().email('请输入有效的邮箱地址').max(100, '邮箱地址不能超过100个字符').optional().or(z.literal('')),
})

// GET /api/user/profile - 获取用户资料
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        avatar: true,
        createdAt: true,
        lastLoginAt: true,
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...user,
        id: user.id.toString(),
      },
    })
  } catch (error) {
    console.error('获取用户资料失败:', error)
    return NextResponse.json(
      { success: false, error: '获取用户资料失败' },
      { status: 500 }
    )
  }
}

// PUT /api/user/profile - 更新用户资料
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = session.user.id

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 解析表单数据
    const formData = await request.formData()
    const nickname = formData.get('nickname') as string
    const email = formData.get('email') as string
    const avatarFile = formData.get('avatar') as File | null

    // 验证基本数据
    const validation = updateProfileSchema.safeParse({
      nickname,
      email: email || undefined,
    })

    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: '数据验证失败',
        details: validation.error.issues,
      }, { status: 400 })
    }

    const updateData: any = {
      nickname: validation.data.nickname,
      email: validation.data.email || null,
      updatedAt: new Date(),
    }

    // 处理头像上传
    if (avatarFile && avatarFile.size > 0) {
      try {
        // 验证文件类型
        if (!avatarFile.type.startsWith('image/')) {
          return NextResponse.json({
            success: false,
            error: '只能上传图片文件',
          }, { status: 400 })
        }

        // 验证文件大小 (2MB)
        if (avatarFile.size > 2 * 1024 * 1024) {
          return NextResponse.json({
            success: false,
            error: '文件大小不能超过2MB',
          }, { status: 400 })
        }

        // 创建上传目录
        const uploadDir = join(process.cwd(), 'public', 'uploads', 'avatars')
        await mkdir(uploadDir, { recursive: true })

        // 生成文件名
        const timestamp = Date.now()
        const extension = avatarFile.name.split('.').pop()
        const fileName = `${userId}_${timestamp}.${extension}`
        const filePath = join(uploadDir, fileName)

        // 保存文件
        const bytes = await avatarFile.arrayBuffer()
        await writeFile(filePath, Buffer.from(bytes))

        // 设置头像URL
        updateData.avatar = `/uploads/avatars/${fileName}`

      } catch (error) {
        console.error('头像上传失败:', error)
        return NextResponse.json({
          success: false,
          error: '头像上传失败',
        }, { status: 500 })
      }
    }

    // 更新用户资料
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        avatar: true,
        updatedAt: true,
      }
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId,
        action: 'UPDATE_PROFILE',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          updatedFields: Object.keys(updateData),
          hasAvatar: !!updateData.avatar,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '个人资料更新成功',
      data: {
        ...updatedUser,
        id: updatedUser.id.toString(),
      },
    })

  } catch (error) {
    console.error('更新用户资料失败:', error)
    return NextResponse.json(
      { success: false, error: '更新用户资料失败' },
      { status: 500 }
    )
  }
}