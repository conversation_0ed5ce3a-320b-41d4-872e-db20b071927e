@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
  background-color: #f0f2f5;
  color: #000000d9;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Ant Design 样式覆盖 */
.ant-layout-sider {
  background: #001529 !important;
}

.ant-menu-dark {
  background: #001529 !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-layout-header {
  background: #fff !important;
  padding: 0 24px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #000000d9 !important;
  font-weight: 600;
}

.ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 自定义工具类 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-ant {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.shadow-ant-hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式隐藏类 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

/* 响应式布局 */
@media (max-width: 576px) {
  .ant-layout-content {
    margin: 12px !important;
    padding: 16px !important;
  }
  
  .ant-layout-header {
    padding: 0 16px !important;
  }
  
  .ant-breadcrumb {
    font-size: 12px !important;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .ant-layout-content {
    margin: 16px !important;
    padding: 20px !important;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .ant-form-item {
    margin-bottom: 16px !important;
  }
}

@media (max-width: 992px) {
  .ant-layout-sider-collapsed {
    flex: 0 0 0 !important;
    min-width: 0 !important;
    width: 0 !important;
  }
}

/* 移动端菜单优化 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh !important;
    z-index: 999 !important;
    left: 0 !important;
    top: 0 !important;
    transition: all 0.3s ease !important;
  }
  
  .ant-layout-sider-collapsed {
    left: -256px !important;
  }
  
  .mobile-sider-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 998;
    display: none;
  }
  
  .mobile-sider-mask.visible {
    display: block;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}