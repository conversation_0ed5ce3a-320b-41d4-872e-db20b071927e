'use client'

import { Layout, Typography, Space, Divider } from 'antd'
import { CopyrightOutlined, HeartFilled } from '@ant-design/icons'

const { Footer: AntFooter } = Layout
const { Text, Link } = Typography

interface FooterProps {
  showFullFooter?: boolean
  className?: string
}

export function Footer({ showFullFooter = true, className }: FooterProps) {
  const currentYear = new Date().getFullYear()

  if (!showFullFooter) {
    // 简化版 Footer
    return (
      <AntFooter className={`text-center py-4 ${className || ''}`}>
        <Text type="secondary" className="text-sm">
          <CopyrightOutlined className="mr-1" />
          {currentYear} 肺功能数据管理平台
        </Text>
      </AntFooter>
    )
  }

  // 完整版 Footer
  return (
    <AntFooter className={`bg-white border-t border-gray-200 ${className || ''}`}>
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* 产品信息 */}
          <div>
            <Text className="text-base font-semibold text-gray-900 block mb-3">
              肺功能数据管理平台
            </Text>
            <Text type="secondary" className="text-sm leading-relaxed">
              专业的医疗数据管理系统，为医疗机构提供高效、安全的数据管理解决方案。
            </Text>
          </div>

          {/* 快速链接 */}
          <div>
            <Text className="text-base font-semibold text-gray-900 block mb-3">
              快速链接
            </Text>
            <Space direction="vertical" size="small">
              <Link href="/dashboard" className="text-sm text-gray-600 hover:text-primary-500">
                仪表板
              </Link>
              <Link href="/forms" className="text-sm text-gray-600 hover:text-primary-500">
                表单管理
              </Link>
              <Link href="/data" className="text-sm text-gray-600 hover:text-primary-500">
                数据管理
              </Link>
              <Link href="/help" className="text-sm text-gray-600 hover:text-primary-500">
                帮助文档
              </Link>
            </Space>
          </div>

          {/* 技术支持 */}
          <div>
            <Text className="text-base font-semibold text-gray-900 block mb-3">
              技术支持
            </Text>
            <Space direction="vertical" size="small">
              <Text className="text-sm text-gray-600">
                技术栈: Next.js + Ant Design
              </Text>
              <Text className="text-sm text-gray-600">
                数据库: 腾讯云轻量数据库
              </Text>
              <Text className="text-sm text-gray-600">
                部署: Docker + 腾讯云服务器
              </Text>
            </Space>
          </div>
        </div>

        <Divider className="my-6" />

        <div className="flex flex-col md:flex-row items-center justify-between">
          <Text type="secondary" className="text-sm mb-2 md:mb-0">
            <CopyrightOutlined className="mr-1" />
            {currentYear} 肺功能数据管理平台. 保留所有权利.
          </Text>
          
          <Text type="secondary" className="text-sm flex items-center">
            Made with <HeartFilled className="text-red-500 mx-1" /> by Development Team
          </Text>
        </div>
      </div>
    </AntFooter>
  )
}