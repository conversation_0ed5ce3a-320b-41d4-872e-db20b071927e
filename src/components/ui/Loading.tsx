'use client'

import { Spin, Empty, Result } from 'antd'
import { LoadingOutlined, InboxOutlined, MehOutlined } from '@ant-design/icons'
import { cn } from '@/lib/utils'

// 加载动画图标
const loadingIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

interface LoadingProps {
  size?: 'small' | 'default' | 'large'
  text?: string
  className?: string
  fullScreen?: boolean
}

export function Loading({ 
  size = 'default', 
  text = '加载中...', 
  className,
  fullScreen = false 
}: LoadingProps) {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          <Spin indicator={loadingIcon} size={size} />
          {text && <div className="mt-4 text-gray-600">{text}</div>}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex items-center justify-center py-8', className)}>
      <div className="text-center">
        <Spin indicator={loadingIcon} size={size} />
        {text && <div className="mt-2 text-gray-600 text-sm">{text}</div>}
      </div>
    </div>
  )
}

// 页面级加载
export function PageLoading({ text = '页面加载中...' }: { text?: string }) {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <Spin indicator={loadingIcon} size="large" />
        <div className="mt-4 text-gray-600">{text}</div>
      </div>
    </div>
  )
}

// 内联加载
export function InlineLoading({ text }: { text?: string }) {
  return (
    <span className="inline-flex items-center space-x-2">
      <Spin indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />} />
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </span>
  )
}

// 空状态组件
interface EmptyStateProps {
  title?: string
  description?: string
  image?: React.ReactNode
  action?: React.ReactNode
  className?: string
  size?: 'small' | 'default' | 'large'
}

export function EmptyState({
  title = '暂无数据',
  description,
  image,
  action,
  className,
  size = 'default'
}: EmptyStateProps) {
  const getImageSize = () => {
    switch (size) {
      case 'small':
        return { height: 60 }
      case 'large':
        return { height: 120 }
      default:
        return { height: 80 }
    }
  }

  return (
    <div className={cn('py-8', className)}>
      <Empty
        image={image || <InboxOutlined style={{ fontSize: getImageSize().height, color: '#d9d9d9' }} />}
        imageStyle={getImageSize()}
        description={
          <div className="text-center">
            <div className="text-gray-500 text-base mb-1">{title}</div>
            {description && (
              <div className="text-gray-400 text-sm">{description}</div>
            )}
          </div>
        }
      >
        {action}
      </Empty>
    </div>
  )
}

// 错误状态组件
interface ErrorStateProps {
  title?: string
  description?: string
  action?: React.ReactNode
  className?: string
}

export function ErrorState({
  title = '出现错误',
  description = '抱歉，页面出现了错误',
  action,
  className
}: ErrorStateProps) {
  return (
    <div className={cn('py-8', className)}>
      <Result
        icon={<MehOutlined style={{ color: '#ff4d4f' }} />}
        title={title}
        subTitle={description}
        extra={action}
      />
    </div>
  )
}

// 无权限状态
export function NoPermissionState({ action }: { action?: React.ReactNode }) {
  return (
    <div className="py-8">
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面"
        extra={action}
      />
    </div>
  )
}

// 404状态
export function NotFoundState({ action }: { action?: React.ReactNode }) {
  return (
    <div className="py-8">
      <Result
        status="404"
        title="404"
        subTitle="抱歉，您访问的页面不存在"
        extra={action}
      />
    </div>
  )
}

// 网络错误状态
export function NetworkErrorState({ onRetry }: { onRetry?: () => void }) {
  return (
    <div className="py-8">
      <Result
        status="500"
        title="网络错误"
        subTitle="网络连接失败，请检查网络设置后重试"
        extra={
          onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
            >
              重新加载
            </button>
          )
        }
      />
    </div>
  )
}

// 骨架屏组件
interface SkeletonProps {
  rows?: number
  avatar?: boolean
  title?: boolean
  loading?: boolean
  children?: React.ReactNode
  className?: string
}

export function Skeleton({
  rows = 3,
  avatar = false,
  title = true,
  loading = true,
  children,
  className
}: SkeletonProps) {
  if (!loading && children) {
    return <>{children}</>
  }

  return (
    <div className={cn('animate-pulse', className)}>
      <div className="flex space-x-4">
        {avatar && (
          <div className="rounded-full bg-gray-200 h-10 w-10 flex-shrink-0"></div>
        )}
        <div className="flex-1 space-y-3">
          {title && (
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          )}
          {Array.from({ length: rows }).map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-4 bg-gray-200 rounded',
                index === rows - 1 ? 'w-1/2' : 'w-full'
              )}
            ></div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 表格骨架屏
export function TableSkeleton({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="space-y-4">
      {/* 表格头部 */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-200 rounded animate-pulse"></div>
        ))}
      </div>
      
      {/* 表格内容 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-100 rounded animate-pulse"></div>
          ))}
        </div>
      ))}
    </div>
  )
}