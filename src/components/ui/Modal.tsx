'use client'

import { Modal as AntModal, ModalProps as AntModalProps } from 'antd'
import { ExclamationCircleOutlined, CheckCircleOutlined, InfoCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { cn } from '@/lib/utils'
import { Button } from './Button'

interface ModalProps extends Omit<AntModalProps, 'footer'> {
  footer?: React.ReactNode | null
  showFooter?: boolean
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  confirmLoading?: boolean
  size?: 'small' | 'medium' | 'large' | 'xl'
}

export function Modal({
  children,
  footer,
  showFooter = true,
  confirmText = '确定',
  cancelText = '取消',
  onConfirm,
  onCancel,
  confirmLoading = false,
  size = 'medium',
  className,
  ...props
}: ModalProps) {
  // 根据 size 设置宽度
  const getWidth = () => {
    switch (size) {
      case 'small':
        return 400
      case 'medium':
        return 600
      case 'large':
        return 800
      case 'xl':
        return 1000
      default:
        return 600
    }
  }

  // 默认 footer
  const defaultFooter = showFooter ? (
    <div className="flex justify-end space-x-2">
      <Button variant="secondary" onClick={onCancel}>
        {cancelText}
      </Button>
      <Button
        variant="primary"
        loading={confirmLoading}
        onClick={onConfirm}
      >
        {confirmText}
      </Button>
    </div>
  ) : null

  return (
    <AntModal
      {...props}
      width={getWidth()}
      footer={footer !== undefined ? footer : defaultFooter}
      onCancel={onCancel}
      className={cn('animate-fadeIn', className)}
      destroyOnClose
      maskClosable={false}
    >
      {children}
    </AntModal>
  )
}

// 确认对话框
interface ConfirmModalProps {
  title?: string
  content?: React.ReactNode
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  confirmText?: string
  cancelText?: string
  type?: 'info' | 'success' | 'warning' | 'error'
  loading?: boolean
}

export function showConfirm({
  title = '确认操作',
  content = '确定要执行此操作吗？',
  onConfirm,
  onCancel,
  confirmText = '确定',
  cancelText = '取消',
  type = 'warning',
  loading = false,
}: ConfirmModalProps) {
  const getIcon = () => {
    switch (type) {
      case 'info':
        return <InfoCircleOutlined className="text-blue-500" />
      case 'success':
        return <CheckCircleOutlined className="text-green-500" />
      case 'warning':
        return <ExclamationCircleOutlined className="text-orange-500" />
      case 'error':
        return <CloseCircleOutlined className="text-red-500" />
      default:
        return <ExclamationCircleOutlined className="text-orange-500" />
    }
  }

  AntModal.confirm({
    title,
    content,
    icon: getIcon(),
    okText: confirmText,
    cancelText,
    okButtonProps: {
      loading,
      type: type === 'error' ? 'primary' : 'primary',
      danger: type === 'error',
    },
    onOk: onConfirm,
    onCancel,
    maskClosable: false,
    autoFocusButton: 'cancel',
  })
}

// 信息对话框
export function showInfo(config: { title?: string; content: React.ReactNode; onOk?: () => void }) {
  AntModal.info({
    title: config.title || '信息',
    content: config.content,
    onOk: config.onOk,
    okText: '知道了',
    icon: <InfoCircleOutlined className="text-blue-500" />,
  })
}

// 成功对话框
export function showSuccess(config: { title?: string; content: React.ReactNode; onOk?: () => void }) {
  AntModal.success({
    title: config.title || '成功',
    content: config.content,
    onOk: config.onOk,
    okText: '知道了',
    icon: <CheckCircleOutlined className="text-green-500" />,
  })
}

// 错误对话框
export function showError(config: { title?: string; content: React.ReactNode; onOk?: () => void }) {
  AntModal.error({
    title: config.title || '错误',
    content: config.content,
    onOk: config.onOk,
    okText: '知道了',
    icon: <CloseCircleOutlined className="text-red-500" />,
  })
}

// 警告对话框
export function showWarning(config: { title?: string; content: React.ReactNode; onOk?: () => void }) {
  AntModal.warning({
    title: config.title || '警告',
    content: config.content,
    onOk: config.onOk,
    okText: '知道了',
    icon: <ExclamationCircleOutlined className="text-orange-500" />,
  })
}

// Drawer 类型的模态框
interface DrawerModalProps extends ModalProps {
  placement?: 'top' | 'right' | 'bottom' | 'left'
}

export function DrawerModal({
  placement = 'right',
  size = 'medium',
  ...props
}: DrawerModalProps) {
  const getWidth = () => {
    if (placement === 'left' || placement === 'right') {
      switch (size) {
        case 'small':
          return 400
        case 'medium':
          return 600
        case 'large':
          return 800
        case 'xl':
          return 1000
        default:
          return 600
      }
    }
    return '100%'
  }

  const getHeight = () => {
    if (placement === 'top' || placement === 'bottom') {
      switch (size) {
        case 'small':
          return 300
        case 'medium':
          return 500
        case 'large':
          return 700
        case 'xl':
          return 900
        default:
          return 500
      }
    }
    return '100%'
  }

  return (
    <AntModal
      {...props}
      width={getWidth()}
      height={getHeight()}
      style={{
        ...props.style,
        ...(placement === 'right' && { marginLeft: 'auto', marginRight: 0 }),
        ...(placement === 'left' && { marginLeft: 0, marginRight: 'auto' }),
        ...(placement === 'top' && { marginTop: 0 }),
        ...(placement === 'bottom' && { marginTop: 'auto', marginBottom: 0 }),
      }}
      className={cn('drawer-modal', `drawer-${placement}`, props.className)}
      transitionName=""
      maskTransitionName=""
    />
  )
}