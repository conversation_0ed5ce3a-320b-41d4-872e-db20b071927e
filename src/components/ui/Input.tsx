'use client'

import { Input as AntInput, InputProps as AntInputProps, Form } from 'antd'
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { cn } from '@/lib/utils'

const { TextArea, Search, Password } = AntInput

interface InputProps extends AntInputProps {
  label?: string
  error?: string
  required?: boolean
  fullWidth?: boolean
}

export function Input({
  label,
  error,
  required = false,
  fullWidth = true,
  className,
  ...props
}: InputProps) {
  return (
    <div className={cn('w-full', !fullWidth && 'inline-block w-auto')}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <AntInput
        {...props}
        status={error ? 'error' : undefined}
        className={cn(
          'transition-all duration-200',
          fullWidth && 'w-full',
          className
        )}
      />
      {error && (
        <div className="mt-1 text-sm text-red-600">{error}</div>
      )}
    </div>
  )
}

// 密码输入框
interface PasswordInputProps extends Omit<AntInputProps, 'type'> {
  label?: string
  error?: string
  required?: boolean
  fullWidth?: boolean
  showStrength?: boolean
}

export function PasswordInput({
  label,
  error,
  required = false,
  fullWidth = true,
  showStrength = false,
  className,
  ...props
}: PasswordInputProps) {
  return (
    <div className={cn('w-full', !fullWidth && 'inline-block w-auto')}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <Password
        {...props}
        status={error ? 'error' : undefined}
        className={cn(
          'transition-all duration-200',
          fullWidth && 'w-full',
          className
        )}
        iconRender={(visible) =>
          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
        }
      />
      {error && (
        <div className="mt-1 text-sm text-red-600">{error}</div>
      )}
    </div>
  )
}

// 文本域
interface TextAreaProps extends React.ComponentProps<typeof TextArea> {
  label?: string
  error?: string
  required?: boolean
  fullWidth?: boolean
}

export function TextAreaInput({
  label,
  error,
  required = false,
  fullWidth = true,
  className,
  ...props
}: TextAreaProps) {
  return (
    <div className={cn('w-full', !fullWidth && 'inline-block w-auto')}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <TextArea
        {...props}
        status={error ? 'error' : undefined}
        className={cn(
          'transition-all duration-200',
          fullWidth && 'w-full',
          className
        )}
      />
      {error && (
        <div className="mt-1 text-sm text-red-600">{error}</div>
      )}
    </div>
  )
}

// 搜索框
interface SearchInputProps extends React.ComponentProps<typeof Search> {
  label?: string
  error?: string
  fullWidth?: boolean
}

export function SearchInput({
  label,
  error,
  fullWidth = true,
  className,
  ...props
}: SearchInputProps) {
  return (
    <div className={cn('w-full', !fullWidth && 'inline-block w-auto')}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <Search
        {...props}
        status={error ? 'error' : undefined}
        className={cn(
          'transition-all duration-200',
          fullWidth && 'w-full',
          className
        )}
      />
      {error && (
        <div className="mt-1 text-sm text-red-600">{error}</div>
      )}
    </div>
  )
}

// 表单项包装器
interface FormInputProps extends InputProps {
  name: string
  rules?: any[]
}

export function FormInput({ name, rules, label, required, ...props }: FormInputProps) {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules}
      required={required}
    >
      <Input {...props} />
    </Form.Item>
  )
}

export function FormPasswordInput({ name, rules, label, required, ...props }: FormInputProps & Omit<PasswordInputProps, 'name' | 'label' | 'required'>) {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules}
      required={required}
    >
      <PasswordInput {...props} />
    </Form.Item>
  )
}

export function FormTextAreaInput({ name, rules, label, required, ...props }: FormInputProps & Omit<TextAreaProps, 'name' | 'label' | 'required'>) {
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules}
      required={required}
    >
      <TextAreaInput {...props} />
    </Form.Item>
  )
}