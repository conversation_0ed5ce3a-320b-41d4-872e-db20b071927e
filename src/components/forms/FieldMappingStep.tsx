'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Button,
  Table,
  Input,
  Select,
  Space,
  Alert,
  Tag,
  Tooltip,
  Switch,
} from 'antd'
import {
  DatabaseOutlined,
  InfoCircleOutlined,
  ArrowRightOutlined,
  EyeOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select

interface FieldMappingStepProps {
  sampleJson: any
  onSubmit: (mapping: Record<string, any>) => void
  initialMapping?: Record<string, any>
}

interface FieldInfo {
  key: string
  originalValue: any
  type: string
  displayName: string
  fieldType: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'text'
  required: boolean
  description: string
}

export function FieldMappingStep({ 
  sampleJson, 
  onSubmit, 
  initialMapping = {} 
}: FieldMappingStepProps) {
  const [fields, setFields] = useState<FieldInfo[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (sampleJson?.entry) {
      const extractedFields = extractFields(sampleJson.entry)
      setFields(extractedFields)
    }
  }, [sampleJson])

  const extractFields = (entry: any): FieldInfo[] => {
    const fieldList: FieldInfo[] = []
    
    Object.keys(entry).forEach(key => {
      // 跳过系统字段
      if (key === 'serial_number' || key.startsWith('info_') || 
          key === 'created_at' || key === 'updated_at' || 
          key === 'creator_name' || key === 'color_mark') {
        return
      }

      const value = entry[key]
      const type = Array.isArray(value) ? 'array' : typeof value
      
      // 从初始映射中获取配置，或使用默认值
      const existingMapping = initialMapping[key] || {}
      
      fieldList.push({
        key,
        originalValue: value,
        type,
        displayName: existingMapping.name || getDefaultDisplayName(key),
        fieldType: existingMapping.fieldType || inferFieldType(value, key),
        required: existingMapping.required || false,
        description: existingMapping.description || '',
      })
    })

    return fieldList.sort((a, b) => {
      // field_ 开头的排在前面，然后是 x_field_，最后是其他
      const getPriority = (key: string) => {
        if (key.startsWith('field_')) return 1
        if (key.startsWith('x_field_')) return 2
        return 3
      }
      return getPriority(a.key) - getPriority(b.key)
    })
  }

  const getDefaultDisplayName = (key: string): string => {
    const commonMappings: Record<string, string> = {
      'field_1': '姓名',
      'field_2': '性别',
      'field_3': '电话',
      'field_4': '选项',
      'field_5': '邮箱',
      'field_6': '年龄',
      'field_7': '地址',
      'x_field_1': '备注',
    }
    return commonMappings[key] || key.replace('field_', '字段').replace('x_field_', '扩展字段')
  }

  const inferFieldType = (value: any, key: string): FieldInfo['fieldType'] => {
    if (Array.isArray(value)) return 'array'
    if (typeof value === 'number') return 'number'
    if (typeof value === 'boolean') return 'boolean'
    if (typeof value === 'object') return 'object'
    
    // 根据字段名推断类型
    if (key.includes('phone') || key.includes('tel') || key === 'field_3') return 'string'
    if (key.includes('email') || key.includes('mail')) return 'string'
    if (key.includes('age') || key.includes('年龄') || key === 'field_6') return 'number'
    if (key.includes('remark') || key.includes('note') || key.startsWith('x_field_')) return 'text'
    if (key.includes('date') || key.includes('time')) return 'date'
    
    return 'string'
  }

  const handleFieldUpdate = (index: number, field: keyof FieldInfo, value: any) => {
    setFields(prev => prev.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    ))
  }

  const handleSubmit = () => {
    const mapping: Record<string, any> = {}
    
    fields.forEach(field => {
      mapping[field.key] = {
        name: field.displayName,
        type: field.fieldType,
        required: field.required,
        description: field.description,
      }
    })

    setLoading(true)
    setTimeout(() => {
      onSubmit(mapping)
      setLoading(false)
    }, 500)
  }

  const renderTypeTag = (type: string) => {
    const colorMap: Record<string, string> = {
      string: 'blue',
      number: 'green',
      boolean: 'orange',
      date: 'purple',
      array: 'cyan',
      object: 'magenta',
      text: 'volcano',
    }
    return <Tag color={colorMap[type] || 'default'}>{type}</Tag>
  }

  const renderValuePreview = (value: any) => {
    if (value === null || value === undefined) {
      return <Text type="secondary">null</Text>
    }
    
    if (Array.isArray(value)) {
      return (
        <Tooltip title={JSON.stringify(value, null, 2)}>
          <Tag icon={<EyeOutlined />}>数组 ({value.length}项)</Tag>
        </Tooltip>
      )
    }
    
    if (typeof value === 'object') {
      return (
        <Tooltip title={JSON.stringify(value, null, 2)}>
          <Tag icon={<EyeOutlined />}>对象</Tag>
        </Tooltip>
      )
    }
    
    const str = String(value)
    return str.length > 20 ? (
      <Tooltip title={str}>
        <Text code>{str.substring(0, 20)}...</Text>
      </Tooltip>
    ) : (
      <Text code>{str}</Text>
    )
  }

  const columns: ColumnsType<FieldInfo> = [
    {
      title: '原始字段',
      dataIndex: 'key',
      width: 120,
      render: (key) => <Text code>{key}</Text>,
    },
    {
      title: '示例值',
      dataIndex: 'originalValue',
      width: 150,
      render: renderValuePreview,
    },
    {
      title: '字段名称',
      dataIndex: 'displayName',
      width: 150,
      render: (_, record, index) => (
        <Input
          value={record.displayName}
          onChange={(e) => handleFieldUpdate(index, 'displayName', e.target.value)}
          placeholder="请输入字段显示名称"
          size="small"
        />
      ),
    },
    {
      title: '数据类型',
      dataIndex: 'fieldType',
      width: 120,
      render: (_, record, index) => (
        <Select
          value={record.fieldType}
          onChange={(value) => handleFieldUpdate(index, 'fieldType', value)}
          size="small"
          style={{ width: '100%' }}
        >
          <Option value="string">字符串</Option>
          <Option value="number">数字</Option>
          <Option value="boolean">布尔值</Option>
          <Option value="date">日期</Option>
          <Option value="array">数组</Option>
          <Option value="object">对象</Option>
          <Option value="text">长文本</Option>
        </Select>
      ),
    },
    {
      title: '必填',
      dataIndex: 'required',
      width: 80,
      render: (_, record, index) => (
        <Switch
          checked={record.required}
          onChange={(checked) => handleFieldUpdate(index, 'required', checked)}
          size="small"
        />
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      render: (_, record, index) => (
        <Input
          value={record.description}
          onChange={(e) => handleFieldUpdate(index, 'description', e.target.value)}
          placeholder="字段描述（可选）"
          size="small"
        />
      ),
    },
  ]

  return (
    <div className="space-y-4">
      <Card>
        <Title level={4} className="mb-4">
          <DatabaseOutlined className="mr-2" />
          配置字段映射
        </Title>

        <Alert
          message="字段映射配置说明"
          description={
            <div className="space-y-1">
              <div>• 为每个字段设置合适的显示名称，便于在界面中识别</div>
              <div>• 选择正确的数据类型，系统将根据类型进行数据验证和处理</div>
              <div>• 标记必填字段，确保数据完整性</div>
              <div>• 添加字段描述，帮助用户理解字段含义</div>
            </div>
          }
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          className="mb-4"
        />

        <div className="mb-4">
          <Text strong>检测到 {fields.length} 个可配置字段</Text>
        </div>

        <Table
          columns={columns}
          dataSource={fields}
          rowKey="key"
          pagination={false}
          size="small"
          scroll={{ x: 800 }}
          className="mb-4"
        />

        <Alert
          message="数据类型说明"
          description={
            <div className="space-y-1 text-sm">
              <div>• <strong>字符串</strong>: 普通文本，如姓名、地址</div>
              <div>• <strong>数字</strong>: 数值类型，如年龄、数量</div>
              <div>• <strong>布尔值</strong>: 是/否选择</div>
              <div>• <strong>日期</strong>: 日期时间类型</div>
              <div>• <strong>数组</strong>: 多选项、列表数据</div>
              <div>• <strong>对象</strong>: 复杂结构数据</div>
              <div>• <strong>长文本</strong>: 多行文本、备注信息</div>
            </div>
          }
          type="warning"
          showIcon
          className="mb-4"
        />

        <div className="flex justify-end">
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            loading={loading}
            icon={<ArrowRightOutlined />}
          >
            配置完成，下一步
          </Button>
        </div>
      </Card>
    </div>
  )
}