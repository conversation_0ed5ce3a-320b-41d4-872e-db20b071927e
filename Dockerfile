# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine AS base

# 安装必要的系统依赖，包括OpenSSL和Prisma所需的库
RUN apk add --no-cache \
    openssl \
    libc6-compat \
    curl

# 检查并安装yarn（如果不存在）
RUN which yarn || npm install -g yarn

# 配置yarn使用腾讯云镜像加速
RUN yarn config set registry https://mirrors.tencent.com/npm/

# 设置工作目录
WORKDIR /app

# 复制依赖文件（优化缓存）
COPY package.json ./
COPY yarn.lock ./
COPY prisma ./prisma/

# 安装依赖（仅生产依赖）
FROM base AS deps
RUN yarn install --production --frozen-lockfile

# 构建阶段
FROM base AS builder
# 先安装依赖（利用Docker缓存）
RUN yarn install --frozen-lockfile
# 再复制代码（代码变更不会影响依赖缓存）
COPY . .

# 生成Prisma客户端（强制重新生成以确保兼容性）
RUN npx prisma generate --force

# 构建应用
RUN yarn build

# 生产阶段
FROM node:18-alpine AS runner

# 安装运行时必要的系统依赖
RUN apk add --no-cache \
    openssl \
    libc6-compat \
    curl

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制必要文件
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=deps /app/node_modules ./node_modules

# 确保Prisma客户端在生产环境中重新生成
RUN npx prisma generate --force

# 复制public目录（从源代码复制，因为standalone模式可能不包含）
COPY public ./public

# 创建上传目录
RUN mkdir -p /app/public/uploads/avatars
RUN chown -R nextjs:nodejs /app/public/uploads

# 设置用户权限
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
CMD ["node", "server.js"]