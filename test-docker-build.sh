#!/bin/bash

echo "🐳 Docker构建速度优化测试脚本"
echo "=================================="
echo "优化内容："
echo "- 统一使用yarn，删除package-lock.json"
echo "- 配置腾讯云NPM镜像加速"
echo "- 优化Docker层缓存策略"
echo "- 创建.dockerignore减少构建上下文"
echo "- 修复Prisma兼容性问题"
echo ""

# 停止现有容器
echo "⏹️  停止现有容器..."
docker-compose -f docker-compose.simple.yml down

# 清理缓存（可选）
echo "🧹 清理Docker构建缓存..."
docker builder prune -f

# 重新构建镜像
echo "🔨 重新构建Docker镜像（使用腾讯云镜像加速）..."
echo "构建开始时间: $(date)"
docker-compose -f docker-compose.simple.yml build --no-cache
echo "构建结束时间: $(date)"

# 启动容器
echo "🚀 启动容器..."
docker-compose -f docker-compose.simple.yml up -d

# 等待容器启动
echo "⏳ 等待容器启动（30秒）..."
sleep 30

# 检查容器状态
echo "📊 检查容器状态..."
docker-compose -f docker-compose.simple.yml ps

# 检查应用健康状态
echo "🔍 检查应用健康状态..."
curl -f http://localhost:3011/api/health || echo "❌ 健康检查失败"

# 查看最近的日志
echo "📜 查看最近的日志..."
docker-compose -f docker-compose.simple.yml logs --tail=50 app

echo ""
echo "✅ 测试完成！"
echo "优化效果检查："
echo "1. 构建时间是否明显缩短（预期2-3分钟）"
echo "2. 依赖下载速度是否更快（使用腾讯云镜像）"
echo "3. 如果看到 'Ready in' 消息且没有Prisma错误，说明修复成功"
echo "4. 现在可以访问 http://localhost:3011 测试登录功能"
echo ""
echo "下次构建时，如果只修改代码（不修改依赖），构建速度会更快！"