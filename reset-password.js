const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const readline = require('readline')

const prisma = new PrismaClient()

// 创建输入接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 隐藏密码输入
function hideInput() {
  const stdin = process.stdin
  stdin.setRawMode(true)
  stdin.resume()
  stdin.setEncoding('utf8')
}

function showInput() {
  const stdin = process.stdin
  stdin.setRawMode(false)
  stdin.pause()
}

// 获取隐藏密码输入
function getHiddenInput(prompt) {
  return new Promise((resolve) => {
    console.log(prompt)
    let password = ''
    
    hideInput()
    process.stdin.on('data', function(char) {
      char = char + ''
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004':
          showInput()
          process.stdin.removeAllListeners('data')
          console.log()
          resolve(password)
          break
        case '\u0003':
          console.log('\n操作已取消')
          process.exit(1)
          break
        case '\u007f': // 退格键
          if (password.length > 0) {
            password = password.slice(0, -1)
            process.stdout.write('\b \b')
          }
          break
        default:
          password += char
          process.stdout.write('*')
          break
      }
    })
  })
}

// 普通输入
function getInput(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      resolve(answer.trim())
    })
  })
}

// 验证密码强度
function validatePassword(password) {
  if (!password) {
    return { valid: false, message: '密码不能为空' }
  }
  if (password.length < 6) {
    return { valid: false, message: '密码长度至少6位' }
  }
  if (password.length > 50) {
    return { valid: false, message: '密码长度不能超过50位' }
  }
  return { valid: true, message: '密码强度符合要求' }
}

// 获取所有用户
async function listAllUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
        lastLoginAt: true,
        loginCount: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    return users
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error)
    return []
  }
}

// 获取用户信息
async function getUserByUsername(username) {
  try {
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        role: true,
        isActive: true,
        passwordHash: true
      }
    })
    return user
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error)
    return null
  }
}

// 重置用户密码
async function resetUserPassword(userId, newPassword) {
  try {
    // 生成新的密码哈希 (使用与登录相同的bcryptjs)
    const saltRounds = 10
    const passwordHash = await bcrypt.hash(newPassword, saltRounds)
    
    // 更新密码
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        passwordHash: passwordHash,
        updatedAt: new Date()
      }
    })
    
    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: userId,
        action: 'PASSWORD_RESET',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          action: 'password_reset_by_script',
          description: '管理员通过Node.js脚本重置用户密码',
          timestamp: new Date().toISOString()
        },
        ipAddress: '127.0.0.1',
        userAgent: 'Node.js Reset Script'
      }
    })
    
    return { success: true, user: updatedUser }
  } catch (error) {
    console.error('❌ 重置密码失败:', error)
    return { success: false, error: error.message }
  }
}

// 测试密码哈希兼容性
async function testPasswordHash(password, hash) {
  try {
    const isValid = await bcrypt.compare(password, hash)
    return isValid
  } catch (error) {
    console.error('❌ 测试密码哈希失败:', error)
    return false
  }
}

// 显示用户列表
function displayUsers(users) {
  console.log('\\n📋 用户列表:')
  console.log('-'.repeat(90))
  console.log('ID'.padEnd(5) + '用户名'.padEnd(15) + '昵称'.padEnd(15) + '邮箱'.padEnd(25) + '角色'.padEnd(10) + '状态'.padEnd(6) + '登录次数')
  console.log('-'.repeat(90))
  
  users.forEach(user => {
    const status = user.isActive ? '激活' : '禁用'
    console.log(
      user.id.toString().padEnd(5) +
      user.username.padEnd(15) +
      (user.nickname || '').padEnd(15) +
      (user.email || '').padEnd(25) +
      user.role.padEnd(10) +
      status.padEnd(6) +
      user.loginCount.toString()
    )
  })
}

// 主菜单
async function showMenu() {
  console.log('\\n请选择操作:')
  console.log('1. 查看所有用户')
  console.log('2. 重置用户密码')
  console.log('3. 测试密码验证')
  console.log('4. 退出')
  
  const choice = await getInput('\\n请输入选择 (1-4): ')
  return choice
}

// 主程序
async function main() {
  console.log('🔐 肺功能数据管理平台 - 密码重置工具 (Node.js版)')
  console.log('='.repeat(60))
  
  try {
    // 测试数据库连接
    console.log('📡 测试数据库连接...')
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    while (true) {
      const choice = await showMenu()
      
      switch (choice) {
        case '1':
          // 查看所有用户
          const users = await listAllUsers()
          if (users.length === 0) {
            console.log('❌ 没有找到任何用户')
          } else {
            displayUsers(users)
          }
          break
          
        case '2':
          // 重置用户密码
          console.log('\\n🔄 重置用户密码')
          const username = await getInput('请输入用户名: ')
          
          if (!username) {
            console.log('❌ 用户名不能为空')
            break
          }
          
          // 查找用户
          const user = await getUserByUsername(username)
          if (!user) {
            console.log(`❌ 用户 '${username}' 不存在`)
            break
          }
          
          // 显示用户信息
          console.log('\\n📋 用户信息:')
          console.log(`ID: ${user.id}`)
          console.log(`用户名: ${user.username}`)
          console.log(`昵称: ${user.nickname || '未设置'}`)
          console.log(`邮箱: ${user.email || '未设置'}`)
          console.log(`角色: ${user.role}`)
          console.log(`状态: ${user.isActive ? '激活' : '禁用'}`)
          
          // 确认操作
          const confirm = await getInput(`\\n⚠️  确认要重置用户 '${username}' 的密码吗? (y/N): `)
          if (confirm.toLowerCase() !== 'y') {
            console.log('❌ 操作已取消')
            break
          }
          
          // 输入新密码
          let newPassword
          while (true) {
            newPassword = await getHiddenInput('请输入新密码: ')
            
            const validation = validatePassword(newPassword)
            if (!validation.valid) {
              console.log(`❌ ${validation.message}`)
              continue
            }
            
            // 确认密码
            const confirmPassword = await getHiddenInput('请再次输入新密码: ')
            if (newPassword !== confirmPassword) {
              console.log('❌ 两次输入的密码不一致')
              continue
            }
            
            break
          }
          
          // 重置密码
          console.log('\\n🔒 正在加密密码...')
          const result = await resetUserPassword(user.id, newPassword)
          
          if (result.success) {
            console.log('✅ 密码重置成功!')
            console.log(`用户: ${username}`)
            console.log(`新密码: ${newPassword}`)
            console.log('⚠️  请妥善保管新密码，并提醒用户及时修改')
            
            // 测试新密码哈希
            console.log('\\n🧪 测试密码哈希兼容性...')
            const updatedUser = await getUserByUsername(username)
            const isHashValid = await testPasswordHash(newPassword, updatedUser.passwordHash)
            console.log(`密码哈希验证: ${isHashValid ? '✅ 通过' : '❌ 失败'}`)
          } else {
            console.log(`❌ 密码重置失败: ${result.error}`)
          }
          break
          
        case '3':
          // 测试密码验证
          console.log('\\n🧪 测试密码验证')
          const testUsername = await getInput('请输入要测试的用户名: ')
          
          if (!testUsername) {
            console.log('❌ 用户名不能为空')
            break
          }
          
          const testUser = await getUserByUsername(testUsername)
          if (!testUser) {
            console.log(`❌ 用户 '${testUsername}' 不存在`)
            break
          }
          
          const testPassword = await getHiddenInput('请输入密码进行验证: ')
          const isPasswordValid = await testPasswordHash(testPassword, testUser.passwordHash)
          
          console.log(`\\n验证结果: ${isPasswordValid ? '✅ 密码正确' : '❌ 密码错误'}`)
          console.log(`用户: ${testUser.username}`)
          console.log(`哈希: ${testUser.passwordHash.substring(0, 20)}...`)
          break
          
        case '4':
          console.log('\\n👋 退出程序')
          return
          
        default:
          console.log('❌ 无效选择，请输入 1-4')
      }
    }
    
  } catch (error) {
    console.error('❌ 程序执行出错:', error)
  } finally {
    await prisma.$disconnect()
    rl.close()
  }
}

// 捕获中断信号
process.on('SIGINT', async () => {
  console.log('\\n\\n👋 程序被用户中断')
  await prisma.$disconnect()
  rl.close()
  process.exit(0)
})

// 运行程序
main().catch(console.error)