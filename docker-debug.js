#!/usr/bin/env node
/**
 * Docker容器内调试脚本
 * 用于在容器内测试认证和数据库连接
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testDatabaseConnection() {
  console.log('🔗 测试数据库连接...')
  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    const userCount = await prisma.user.count()
    console.log(`📊 用户总数: ${userCount}`)
    
    return true
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
    return false
  }
}

async function testPasswordVerification(username, password) {
  console.log(`\\n🔍 测试用户密码验证: ${username}`)
  
  try {
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        passwordHash: true,
        isActive: true,
        role: true
      }
    })
    
    if (!user) {
      console.log(`❌ 用户不存在: ${username}`)
      return false
    }
    
    console.log(`✅ 用户找到:`)
    console.log(`   ID: ${user.id}`)
    console.log(`   用户名: ${user.username}`)
    console.log(`   角色: ${user.role}`)
    console.log(`   状态: ${user.isActive ? '激活' : '禁用'}`)
    console.log(`   密码哈希: ${user.passwordHash.substring(0, 30)}...`)
    
    if (!user.isActive) {
      console.log(`❌ 用户未激活`)
      return false
    }
    
    console.log(`\\n🔑 开始密码验证...`)
    const isValid = await bcrypt.compare(password, user.passwordHash)
    console.log(`🔓 密码验证结果: ${isValid ? '✅ 通过' : '❌ 失败'}`)
    
    return isValid
  } catch (error) {
    console.error(`❌ 密码验证出错:`, error)
    return false
  }
}

async function checkEnvironmentVariables() {
  console.log('\\n🌍 检查环境变量:')
  const envVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'JWT_SECRET',
    'LOG_LEVEL'
  ]
  
  envVars.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      if (varName.includes('SECRET') || varName.includes('PASSWORD')) {
        console.log(`   ${varName}: ${'*'.repeat(20)}`)
      } else if (varName === 'DATABASE_URL') {
        console.log(`   ${varName}: ${value.substring(0, 30)}...`)
      } else {
        console.log(`   ${varName}: ${value}`)
      }
    } else {
      console.log(`   ${varName}: ❌ 未设置`)
    }
  })
}

async function listUsers() {
  console.log('\\n👥 用户列表:')
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        role: true,
        isActive: true,
        loginCount: true,
        lastLoginAt: true
      },
      orderBy: { createdAt: 'desc' }
    })
    
    console.log('-'.repeat(80))
    console.log('ID'.padEnd(5) + '用户名'.padEnd(15) + '昵称'.padEnd(15) + '角色'.padEnd(10) + '状态'.padEnd(8) + '登录次数')
    console.log('-'.repeat(80))
    
    users.forEach(user => {
      const status = user.isActive ? '激活' : '禁用'
      console.log(
        user.id.toString().padEnd(5) +
        user.username.padEnd(15) +
        (user.nickname || '').padEnd(15) +
        user.role.padEnd(10) +
        status.padEnd(8) +
        user.loginCount.toString()
      )
    })
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error)
  }
}

async function testBcryptVersions() {
  console.log('\\n🔧 测试bcrypt版本兼容性:')
  const testPassword = 'test123'
  
  try {
    // 测试不同的salt rounds
    const saltRounds = [10, 12]
    
    for (const rounds of saltRounds) {
      console.log(`\\n📝 测试 salt rounds = ${rounds}:`)
      const hash = await bcrypt.hash(testPassword, rounds)
      console.log(`   哈希: ${hash.substring(0, 30)}...`)
      
      const isValid = await bcrypt.compare(testPassword, hash)
      console.log(`   验证: ${isValid ? '✅ 通过' : '❌ 失败'}`)
    }
  } catch (error) {
    console.error('❌ bcrypt测试失败:', error)
  }
}

async function main() {
  console.log('🐳 Docker容器调试脚本')
  console.log('='.repeat(50))
  
  // 检查环境变量
  checkEnvironmentVariables()
  
  // 测试数据库连接
  const dbConnected = await testDatabaseConnection()
  if (!dbConnected) {
    console.log('❌ 数据库连接失败，退出')
    process.exit(1)
  }
  
  // 列出用户
  await listUsers()
  
  // 测试bcrypt
  await testBcryptVersions()
  
  // 测试admin用户密码验证
  console.log('\\n' + '='.repeat(50))
  console.log('🔐 测试admin用户登录:')
  await testPasswordVerification('admin', 'a355555')
  
  console.log('\\n🎉 调试脚本执行完成')
}

// 运行主函数
main()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect()
  })