# 肺功能数据管理平台 API 文档

## 概述

本文档描述了肺功能数据管理平台的 RESTful API 接口。所有 API 请求都需要进行身份验证，除非另有说明。

### 基础信息

- **Base URL**: `http://localhost:3000/api` (开发环境)
- **Content-Type**: `application/json`
- **Authentication**: 基于 NextAuth.js 的会话认证

## 认证

### 登录
```http
POST /api/auth/signin
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:
```json
{
  "user": {
    "id": "1",
    "username": "admin",
    "nickname": "系统管理员",
    "role": "admin"
  }
}
```

## 用户管理

### 获取用户列表
```http
GET /api/users?page=1&limit=10&search=
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "1",
        "username": "admin",
        "nickname": "系统管理员",
        "email": "<EMAIL>",
        "role": "admin",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

### 创建用户
```http
POST /api/users
```

**请求体**:
```json
{
  "username": "newuser",
  "nickname": "新用户",
  "email": "<EMAIL>",
  "role": "user"
}
```

**响应**:
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "user": {
      "id": "2",
      "username": "newuser",
      "nickname": "新用户",
      "role": "user"
    },
    "initialPassword": "randomPassword123"
  }
}
```

### 更新用户
```http
PUT /api/users/{id}
```

### 删除用户
```http
DELETE /api/users/{id}
```

### 重置用户密码
```http
POST /api/users/{id}/reset-password
```

## 表单配置管理

### 获取表单配置列表
```http
GET /api/forms?page=1&limit=10&search=
```

**响应**:
```json
{
  "success": true,
  "data": {
    "forms": [
      {
        "id": 1,
        "formId": "ZFs2eo",
        "formName": "预约免费肺功能检查",
        "isActive": true,
        "fieldCount": 5,
        "webhookUrl": "http://localhost:3000/api/webhook/ZFs2eo",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

### 创建表单配置
```http
POST /api/forms
```

**请求体**:
```json
{
  "formId": "ZFs2eo",
  "formName": "预约免费肺功能检查",
  "sampleJson": {
    "form": "ZFs2eo",
    "form_name": "预约免费肺功能检查",
    "entry": {
      "serial_number": 123,
      "field_1": "张三",
      "field_2": "选项1",
      "field_3": "13812345678"
    }
  },
  "fieldMapping": {
    "field_1": {
      "name": "姓名",
      "type": "string",
      "required": true,
      "description": "用户姓名"
    },
    "field_2": {
      "name": "性别",
      "type": "string",
      "required": true,
      "description": "用户性别"
    },
    "field_3": {
      "name": "电话",
      "type": "string",
      "required": true,
      "description": "联系电话"
    }
  }
}
```

### 获取表单配置详情
```http
GET /api/forms/{formId}
```

### 更新表单配置
```http
PUT /api/forms/{formId}
```

### 删除表单配置
```http
DELETE /api/forms/{formId}
```

## 数据管理

### 获取表单数据
```http
GET /api/data/{formId}?page=1&limit=10&serial_number=123&field_1=张三
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `serial_number`: 按序号搜索
- `field_*`: 按字段值搜索
- `created_at_start`: 开始日期
- `created_at_end`: 结束日期

**响应**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": "1",
        "serial_number": 123,
        "field_1": "张三",
        "field_2": "男",
        "field_3": "13812345678",
        "created_at": "2024-01-01T10:00:00.000Z",
        "raw_data": {
          "form": "ZFs2eo",
          "entry": {
            "serial_number": 123,
            "field_1": "张三"
          }
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    },
    "formConfig": {
      "formId": "ZFs2eo",
      "formName": "预约免费肺功能检查",
      "fieldMapping": {
        "field_1": {
          "name": "姓名",
          "type": "string"
        }
      }
    }
  }
}
```

### 批量删除数据
```http
DELETE /api/data/{formId}/batch
```

**请求体**:
```json
{
  "ids": ["1", "2", "3"]
}
```

### 批量更新数据
```http
PUT /api/data/{formId}/batch
```

**请求体**:
```json
{
  "ids": ["1", "2", "3"],
  "updateData": {
    "field_1": "新值"
  }
}
```

### 导出数据
```http
GET /api/data/{formId}/export?format=excel&ids=1,2,3
```

**查询参数**:
- `format`: 导出格式 (`excel` 或 `csv`)
- `ids`: 指定导出的数据ID (可选)
- 其他搜索参数同获取数据接口

## Webhook 接收

### 接收金数据推送
```http
POST /api/webhook/{formId}
```

这是金数据系统调用的接口，用于接收表单提交数据。

**请求体** (金数据格式):
```json
{
  "form": "ZFs2eo",
  "form_name": "预约免费肺功能检查",
  "entry": {
    "serial_number": 123,
    "field_1": "张三",
    "field_2": "男",
    "field_3": "13812345678",
    "created_at": "2024-01-01T10:00:00.000Z"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "Webhook data processed successfully",
  "recordId": 1,
  "formId": "ZFs2eo",
  "serialNumber": 123
}
```

### 获取Webhook信息
```http
GET /api/webhook/{formId}
```

## 个人资料管理

### 获取个人资料
```http
GET /api/user/profile
```

### 更新个人资料
```http
PUT /api/user/profile
```

**请求体** (FormData):
```
nickname: 新昵称
email: <EMAIL>
avatar: [文件]
```

### 修改密码
```http
PUT /api/user/password
```

**请求体**:
```json
{
  "currentPassword": "oldPassword",
  "newPassword": "newPassword123"
}
```

## 系统日志

### 获取系统日志
```http
GET /api/logs?page=1&limit=20&action=LOGIN&resource=User&userId=1&startDate=2024-01-01&endDate=2024-01-31
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `action`: 操作类型
- `resource`: 资源类型
- `userId`: 用户ID
- `ipAddress`: IP地址
- `startDate`: 开始日期
- `endDate`: 结束日期

## 仪表板

### 获取仪表板数据
```http
GET /api/dashboard
```

**响应**:
```json
{
  "success": true,
  "data": {
    "stats": {
      "totalUsers": 5,
      "activeUsers": 4,
      "totalForms": 3,
      "activeForms": 2,
      "totalRecords": 150,
      "todayRecords": 12,
      "webhookSuccess": 145,
      "webhookErrors": 3
    },
    "recentActivities": [
      {
        "id": "1",
        "action": "LOGIN",
        "resource": "User",
        "createdAt": "2024-01-01T10:00:00.000Z",
        "user": {
          "username": "admin",
          "nickname": "系统管理员"
        }
      }
    ],
    "formStats": [
      {
        "formId": "ZFs2eo",
        "formName": "预约免费肺功能检查",
        "recordCount": 100,
        "todayCount": 8,
        "isActive": true
      }
    ]
  }
}
```

## 健康检查

### 系统健康检查
```http
GET /api/health
```

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T10:00:00.000Z",
  "uptime": 3600,
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": {
      "status": "healthy",
      "latency": "15ms"
    },
    "cache": {
      "status": "healthy",
      "size": 42,
      "keys": 15
    },
    "disk": {
      "status": "healthy"
    },
    "memory": {
      "status": "healthy",
      "usage": {
        "rss": "120MB",
        "heapTotal": "80MB",
        "heapUsed": "65MB",
        "external": "25MB"
      }
    }
  },
  "latency": "25ms"
}
```

## 错误响应

所有 API 在发生错误时都会返回以下格式:

```json
{
  "success": false,
  "error": "错误描述",
  "details": "详细错误信息"
}
```

常见 HTTP 状态码:
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误

## 限流

API 实施了限流策略:
- 一般API: 10 requests/second
- 登录API: 5 requests/minute
- 批量操作: 更严格的限制

超出限制时会返回 `429 Too Many Requests`。

## 安全

- 所有密码都使用 bcrypt 加密存储
- 使用 HTTPS 传输敏感数据
- 实施 CSRF 保护
- 设置安全响应头
- 记录所有关键操作日志