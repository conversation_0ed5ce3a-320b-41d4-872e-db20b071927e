# 肺功能数据管理平台 - 技术规范文档

## 技术架构概览

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   金数据平台     │────▶│   Webhook API   │────▶│   数据库存储     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                               │
                               ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面       │◀────│   Next.js App   │────▶│   用户认证       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈详细规范

#### 前端技术栈
- **框架**: Next.js 14.2.x (App Router)
- **UI库**: Ant Design 5.12.x
- **样式**: Tailwind CSS 3.4.x + Ant Design 主题
- **状态管理**: Zustand 4.4.x
- **表单处理**: React Hook Form 7.48.x
- **图标**: @ant-design/icons 5.2.x
- **时间处理**: dayjs 1.11.x
- **文件处理**: xlsx 0.18.x (导出Excel)

#### 后端技术栈
- **运行时**: Node.js 18.x
- **框架**: Next.js API Routes
- **数据库**: MySQL 8.0 (腾讯云轻量数据库)
- **ORM**: Prisma 5.7.x
- **认证**: NextAuth.js 4.24.x
- **密码加密**: bcryptjs 2.4.x
- **日志**: Winston 3.11.x
- **验证**: Zod 3.22.x

#### 开发工具
- **TypeScript**: 5.3.x
- **ESLint**: 8.56.x
- **Prettier**: 3.1.x
- **Husky**: 8.0.x (Git hooks)

#### 部署技术
- **容器化**: Docker + Docker Compose
- **构建**: Next.js Static Export
- **服务器**: 腾讯云轻量应用服务器
- **反向代理**: Nginx (可选)

---

## 数据库设计规范

### 表结构设计

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    nickname VARCHAR(100) COMMENT '昵称',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) COMMENT '邮箱',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

#### 2. 表单配置表 (form_configs)
```sql
CREATE TABLE form_configs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    form_id VARCHAR(50) UNIQUE NOT NULL COMMENT '表单ID',
    form_name VARCHAR(200) NOT NULL COMMENT '表单名称',
    field_mapping JSON NOT NULL COMMENT '字段映射配置',
    table_name VARCHAR(100) COMMENT '对应数据表名',
    table_created BOOLEAN DEFAULT FALSE COMMENT '数据表是否已创建',
    webhook_url VARCHAR(500) COMMENT 'Webhook URL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by INT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_form_id (form_id),
    INDEX idx_form_name (form_name),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单配置表';
```

#### 3. 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    level ENUM('debug', 'info', 'warn', 'error') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    context JSON COMMENT '上下文信息',
    user_id INT COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_level (level),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';
```

#### 4. 动态表单数据表 (form_data_{form_id})
```sql
-- 示例：form_data_ZFs2eo
CREATE TABLE form_data_ZFs2eo (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据ID',
    form_id VARCHAR(50) NOT NULL COMMENT '表单ID',
    serial_number INT COMMENT '序列号',
    raw_data JSON NOT NULL COMMENT '原始JSON数据',
    
    -- 动态字段 (根据表单配置生成)
    field_1 VARCHAR(255) COMMENT '姓名',
    field_2 VARCHAR(255) COMMENT '选项',
    field_3 VARCHAR(20) COMMENT '电话',
    field_4 JSON COMMENT '多选项',
    field_6 INT COMMENT '数字字段',
    field_7 VARCHAR(255) COMMENT '其他选项',
    x_field_1 TEXT COMMENT '文本字段',
    
    -- 元数据字段
    color_mark VARCHAR(50) COMMENT '颜色标记',
    creator_name VARCHAR(100) COMMENT '创建者',
    info_filling_duration INT COMMENT '填写时长',
    info_platform VARCHAR(100) COMMENT '平台',
    info_os VARCHAR(100) COMMENT '操作系统',
    info_browser VARCHAR(200) COMMENT '浏览器',
    info_remote_ip VARCHAR(45) COMMENT 'IP地址',
    info_region_province VARCHAR(100) COMMENT '省份',
    info_region_city VARCHAR(100) COMMENT '城市',
    info_region_district VARCHAR(100) COMMENT '区县',
    info_region_street VARCHAR(200) COMMENT '街道',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_form_id (form_id),
    INDEX idx_serial_number (serial_number),
    INDEX idx_created_at (created_at),
    INDEX idx_field_1 (field_1),
    INDEX idx_field_3 (field_3)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单数据表-预约免费肺功能检查';
```

### 索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: username, form_id 等唯一字段
- **普通索引**: 常用查询字段，如 created_at, form_id
- **复合索引**: 多字段组合查询场景

---

## API接口规范

### 接口设计原则
- RESTful API 设计风格
- 统一的响应格式
- 完整的错误处理
- 请求参数验证
- 接口版本控制

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: string
  timestamp: string
}

// 成功响应
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}

// 错误响应
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 接口列表

#### 认证相关 (/api/auth)
```typescript
// 登录
POST /api/auth/signin
Body: { username: string, password: string, remember?: boolean }
Response: { user: User, token: string }

// 登出
POST /api/auth/signout
Response: { success: true }

// 获取当前用户
GET /api/auth/me
Response: { user: User }
```

#### 用户管理 (/api/users)
```typescript
// 获取用户列表
GET /api/users
Query: { page?: number, limit?: number, search?: string }
Response: { users: User[], total: number, page: number, limit: number }

// 创建用户
POST /api/users
Body: { username: string, nickname?: string, email?: string, password: string }
Response: { user: User }

// 更新用户信息
PUT /api/users/[id]
Body: { nickname?: string, email?: string }
Response: { user: User }

// 修改密码
PUT /api/users/[id]/password
Body: { currentPassword: string, newPassword: string }
Response: { success: true }

// 上传头像
POST /api/users/[id]/avatar
Body: FormData (file)
Response: { avatarUrl: string }
```

#### 表单配置 (/api/forms)
```typescript
// 获取表单配置列表
GET /api/forms
Response: { forms: FormConfig[] }

// 创建表单配置
POST /api/forms
Body: { formId: string, formName: string, sampleJson: object }
Response: { form: FormConfig }

// 更新表单配置
PUT /api/forms/[id]
Body: { formName?: string, fieldMapping?: object }
Response: { form: FormConfig }

// 删除表单配置
DELETE /api/forms/[id]
Response: { success: true }
```

#### 数据管理 (/api/data)
```typescript
// 获取表单数据
GET /api/data/[formId]
Query: { page?: number, limit?: number, search?: string, filters?: object }
Response: { data: FormData[], total: number, page: number, limit: number }

// 创建数据
POST /api/data/[formId]
Body: { ...formFields }
Response: { data: FormData }

// 更新数据
PUT /api/data/[formId]/[id]
Body: { ...formFields }
Response: { data: FormData }

// 删除数据
DELETE /api/data/[formId]/[id]
Response: { success: true }

// 批量删除
DELETE /api/data/[formId]/batch
Body: { ids: number[] }
Response: { success: true, count: number }

// 导出数据
GET /api/data/[formId]/export
Query: { format: 'excel' | 'csv', filters?: object }
Response: File download
```

#### Webhook接收 (/api/webhook)
```typescript
// 接收金数据推送
POST /api/webhook/[formId]
Body: JinshujuWebhookData
Response: { success: true, id: number }
```

---

## 前端架构规范

### 目录结构
```
src/
├── app/                      # Next.js App Router
│   ├── (auth)/              # 认证路由组
│   ├── (dashboard)/         # 仪表板路由组
│   ├── api/                 # API Routes
│   ├── globals.css          # 全局样式
│   └── layout.tsx           # 根布局
├── components/              # 组件库
│   ├── ui/                  # 基础UI组件
│   ├── forms/               # 表单组件
│   ├── data/                # 数据组件
│   ├── user/                # 用户组件
│   └── layout/              # 布局组件
├── lib/                     # 工具库
│   ├── auth.ts              # 认证配置
│   ├── db.ts                # 数据库连接
│   ├── logger.ts            # 日志工具
│   ├── utils.ts             # 工具函数
│   └── constants.ts         # 常量定义
├── types/                   # 类型定义
│   ├── auth.ts
│   ├── forms.ts
│   ├── data.ts
│   └── api.ts
└── store/                   # 状态管理
    ├── auth.ts
    ├── forms.ts
    └── ui.ts
```

### 组件设计规范

#### 1. 组件命名规范
- **页面组件**: PascalCase, 如 `UserProfilePage`
- **UI组件**: PascalCase, 如 `DataTable`
- **Hook**: camelCase + use前缀, 如 `useAuth`
- **工具函数**: camelCase, 如 `formatDate`

#### 2. 组件结构模板
```typescript
// components/ui/DataTable.tsx
'use client'

import React from 'react'
import { Table, Button } from 'antd'
import type { TableProps } from 'antd'

interface DataTableProps<T = any> extends Omit<TableProps<T>, 'dataSource'> {
  data: T[]
  loading?: boolean
  onRefresh?: () => void
}

export function DataTable<T extends { id: number }>({ 
  data, 
  loading, 
  onRefresh, 
  ...tableProps 
}: DataTableProps<T>) {
  return (
    <div className="data-table">
      <div className="data-table-header">
        <Button onClick={onRefresh} loading={loading}>
          刷新
        </Button>
      </div>
      <Table
        dataSource={data}
        loading={loading}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        {...tableProps}
      />
    </div>
  )
}
```

#### 3. Hook 设计规范
```typescript
// lib/hooks/useAuth.ts
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export function useAuth() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }

  const logout = async () => {
    await signOut()
    router.push('/login')
  }

  return {
    user: session?.user,
    isLoading: status === 'loading',
    isAuthenticated: !!session,
    login,
    logout
  }
}
```

### 状态管理规范

#### Zustand Store 结构
```typescript
// store/auth.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: (user, token) => {
        set({ user, token, isAuthenticated: true })
      },
      
      logout: () => {
        set({ user: null, token: null, isAuthenticated: false })
      },
      
      updateUser: (userData) => {
        const currentUser = get().user
        if (currentUser) {
          set({ user: { ...currentUser, ...userData } })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        token: state.token, 
        isAuthenticated: state.isAuthenticated 
      })
    }
  )
)
```

---

## 样式设计规范

### Tailwind CSS 配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6f7ff',
          100: '#bae7ff',
          200: '#91d5ff',
          300: '#69c0ff',
          400: '#40a9ff',
          500: '#1890ff', // 主色调
          600: '#096dd9',
          700: '#0050b3',
          800: '#003a8c',
          900: '#002766',
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      }
    },
  },
  plugins: [],
}
```

### Ant Design 主题配置
```typescript
// lib/theme.ts
import type { ThemeConfig } from 'antd'

export const antdTheme: ThemeConfig = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 6,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  components: {
    Layout: {
      siderBg: '#001529',
      headerBg: '#fff',
      bodyBg: '#f0f2f5'
    },
    Menu: {
      darkItemBg: '#001529',
      darkItemSelectedBg: '#1890ff'
    },
    Table: {
      headerBg: '#fafafa',
      headerColor: '#000000d9'
    }
  }
}
```

---

## 安全规范

### 1. 认证安全
```typescript
// lib/auth.ts
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// 密码加密
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// 密码验证
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// JWT 生成
export function generateToken(payload: object): string {
  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '7d',
    issuer: 'lung-function-admin'
  })
}
```

### 2. 输入验证
```typescript
// lib/validation.ts
import { z } from 'zod'

export const loginSchema = z.object({
  username: z.string().min(3).max(50),
  password: z.string().min(6).max(100),
  remember: z.boolean().optional()
})

export const userSchema = z.object({
  username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/),
  nickname: z.string().max(100).optional(),
  email: z.string().email().optional(),
  password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
})
```

### 3. 中间件保护
```typescript
// middleware.ts
import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    // API 路由保护
    if (req.nextUrl.pathname.startsWith('/api/')) {
      const token = req.nextauth.token
      if (!token && !req.nextUrl.pathname.startsWith('/api/auth')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }
    
    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        if (req.nextUrl.pathname.startsWith('/dashboard')) {
          return !!token
        }
        return true
      }
    }
  }
)

export const config = {
  matcher: ['/dashboard/:path*', '/api/:path*']
}
```

---

## 性能优化规范

### 1. 前端性能优化
```typescript
// 代码分割
const LazyDataTable = lazy(() => import('@/components/data/DataTable'))

// 图片优化
import Image from 'next/image'

// API 缓存
const fetcher = (url: string) => fetch(url).then(res => res.json())
const { data, error, isLoading } = useSWR('/api/data', fetcher, {
  revalidateOnFocus: false,
  revalidateOnReconnect: false
})
```

### 2. 数据库性能优化
```typescript
// Prisma 查询优化
const users = await prisma.user.findMany({
  select: {
    id: true,
    username: true,
    nickname: true,
    email: true,
    createdAt: true
  },
  take: pageSize,
  skip: (page - 1) * pageSize,
  orderBy: {
    createdAt: 'desc'
  }
})

// 批量操作
const result = await prisma.formData.deleteMany({
  where: {
    id: {
      in: ids
    }
  }
})
```

---

## 部署配置规范

### Docker 配置
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Build the app
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: lung_function_web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: lung_function_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

---

## 日志和监控规范

### 日志配置
```typescript
// lib/logger.ts
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'lung-function-admin' },
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
})

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }))
}

export default logger
```

### 错误处理
```typescript
// lib/error-handler.ts
import logger from './logger'

export class AppError extends Error {
  statusCode: number
  isOperational: boolean

  constructor(message: string, statusCode: number) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true

    Error.captureStackTrace(this, this.constructor)
  }
}

export function handleError(error: Error, req?: any) {
  logger.error({
    message: error.message,
    stack: error.stack,
    url: req?.url,
    method: req?.method,
    ip: req?.ip,
    timestamp: new Date().toISOString()
  })
}
```

---

## 测试规范

### 单元测试配置
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
}
```

### 测试示例
```typescript
// __tests__/components/DataTable.test.tsx
import { render, screen } from '@testing-library/react'
import { DataTable } from '@/components/ui/DataTable'

describe('DataTable', () => {
  const mockData = [
    { id: 1, name: '张三', phone: '13800138000' },
    { id: 2, name: '李四', phone: '13800138001' }
  ]

  it('renders table with data', () => {
    render(<DataTable data={mockData} />)
    
    expect(screen.getByText('张三')).toBeInTheDocument()
    expect(screen.getByText('李四')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<DataTable data={[]} loading={true} />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
```

---

## 代码质量规范

### ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'next/core-web-vitals',
    '@typescript-eslint/recommended'
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

### Git Hooks 配置
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ]
  }
}
```

这份技术规范文档为整个项目的开发提供了详细的指导原则和标准，确保代码质量和项目的可维护性。