const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    // 生成测试用户凭据
    const username = 'testuser'
    const password = 'test123456'
    const email = '<EMAIL>'
    const nickname = '测试用户'
    
    console.log('🔐 生成测试用户凭据...')
    console.log(`用户名: ${username}`)
    console.log(`密码: ${password}`)
    console.log(`邮箱: ${email}`)
    
    // 加密密码
    const saltRounds = 10
    const passwordHash = await bcrypt.hash(password, saltRounds)
    
    console.log('\n🔒 密码已加密')
    
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { username: username }
    })
    
    if (existingUser) {
      console.log('\n📝 更新现有测试用户...')
      
      const updatedUser = await prisma.user.update({
        where: { username: username },
        data: {
          passwordHash: passwordHash,
          email: email,
          nickname: nickname,
          role: 'user',
          isActive: true,
          updatedAt: new Date()
        }
      })
      
      console.log('✅ 测试用户更新成功!')
      console.log(`用户ID: ${updatedUser.id}`)
      console.log(`用户名: ${updatedUser.username}`)
      console.log(`角色: ${updatedUser.role}`)
      console.log(`状态: ${updatedUser.isActive ? '激活' : '禁用'}`)
      
    } else {
      console.log('\n➕ 创建新测试用户...')
      
      const newUser = await prisma.user.create({
        data: {
          username: username,
          passwordHash: passwordHash,
          email: email,
          nickname: nickname,
          role: 'user',
          isActive: true,
          loginCount: 0
        }
      })
      
      console.log('✅ 测试用户创建成功!')
      console.log(`用户ID: ${newUser.id}`)
      console.log(`用户名: ${newUser.username}`)
      console.log(`角色: ${newUser.role}`)
      console.log(`状态: ${newUser.isActive ? '激活' : '禁用'}`)
    }
    
    console.log('\n🎉 操作完成! 测试用户凭据:')
    console.log(`用户名: ${username}`)
    console.log(`密码: ${password}`)
    
  } catch (error) {
    console.error('❌ 操作失败:', error)
    
    if (error.code === 'P2002') {
      console.log('用户名已存在')
    } else if (error.code === 'P2025') {
      console.log('用户不存在')
    } else {
      console.log('数据库操作错误:', error.message)
    }
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
createTestUser()