#!/bin/bash

# 部署脚本
set -e

echo "🚀 开始部署肺功能数据管理平台..."

# 检查环境变量
if [ -z "$NODE_ENV" ]; then
    export NODE_ENV=production
fi

echo "📋 当前环境: $NODE_ENV"

# 检查必要的文件
if [ ! -f ".env.production" ] && [ "$NODE_ENV" = "production" ]; then
    echo "❌ 生产环境配置文件 .env.production 不存在"
    exit 1
fi

if [ ! -f "Dockerfile" ]; then
    echo "❌ Dockerfile 不存在"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down || true

# 清理旧镜像（可选）
if [ "$1" = "--clean" ]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker image prune -f
fi

# 构建新镜像
echo "🔨 构建新镜像..."
docker-compose build --no-cache

# 检查数据库连接
echo "🔌 检查数据库连接..."
if ! docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
    echo "⚠️  数据库未运行，正在启动..."
    docker-compose up -d mysql
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 30
    
    # 再次检查
    for i in {1..30}; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            echo "✅ 数据库连接成功"
            break
        fi
        echo "等待数据库启动... ($i/30)"
        sleep 2
    done
fi

# 运行数据库迁移
echo "📊 运行数据库迁移..."
docker-compose run --rm app npx prisma db push --accept-data-loss

# 启动所有服务
echo "🏃 启动所有服务..."
if [ "$NODE_ENV" = "production" ]; then
    docker-compose --profile production up -d
else
    docker-compose up -d
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🏥 进行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        echo "✅ 应用启动成功！"
        break
    fi
    echo "等待应用启动... ($i/30)"
    sleep 2
done

# 显示服务状态
echo "📊 服务状态:"
docker-compose ps

# 显示日志（最后20行）
echo "📜 最新日志:"
docker-compose logs --tail=20 app

echo ""
echo "🎉 部署完成！"
echo "📱 应用地址: http://localhost:3000"
echo "🔧 管理员账户: admin / admin123"
echo ""
echo "💡 常用命令:"
echo "  查看日志: docker-compose logs -f app"
echo "  重启服务: docker-compose restart app"
echo "  停止服务: docker-compose down"
echo "  查看状态: docker-compose ps"