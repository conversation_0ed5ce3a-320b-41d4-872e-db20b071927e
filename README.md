# 肺功能数据管理平台

一个现代化的全栈数据管理平台，专为处理金数据(Jinshuju)webhook数据而设计，提供完整的用户管理、表单配置、数据管理和导出功能。

## 🚀 技术栈

- **前端框架**: Next.js 14 (App Router)
- **UI 组件库**: Ant Design 5.x
- **样式方案**: Tailwind CSS
- **数据库**: 腾讯云轻量数据库 (MySQL)
- **ORM**: Prisma
- **认证**: NextAuth.js + bcrypt
- **状态管理**: Zustand
- **部署**: Docker + Docker Compose

## 📋 主要功能

### 🔐 用户认证系统
- 用户名密码登录
- 密码加密存储
- 会话管理
- 个人设置管理

### 📝 表单配置管理
- JSON 格式表单配置
- 动态字段映射
- 自动数据表创建
- 表单列表管理

### 🔄 Webhook 数据接收
- 金数据 webhook 自动接收
- 数据格式验证和转换
- 错误处理和重试机制
- 完整的操作日志

### 📊 数据管理界面
- 数据表格展示
- 增删改查操作
- 批量操作支持
- 高级搜索和筛选
- Excel/CSV 数据导出

### ⚙️ 系统管理
- 用户管理
- 系统日志查看
- 操作审计记录

## 🛠️ 开发环境设置

### 环境要求
- Node.js 18+
- Yarn 或 npm
- Docker Desktop (用于部署)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd lung-function-admin
   ```

2. **安装依赖**
   ```bash
   yarn install
   # 或
   npm install
   ```

3. **环境配置**
   ```bash
   cp .env.example .env.local
   # 编辑 .env.local 文件，配置数据库连接等信息
   ```

4. **数据库设置**
   ```bash
   # 生成 Prisma 客户端
   yarn db:generate
   
   # 运行数据库迁移
   yarn db:migrate
   ```

5. **启动开发服务器**
   ```bash
   yarn dev
   ```

6. **访问应用**
   ```
   http://localhost:3000
   ```

## 📝 开发命令

```bash
# 开发相关
yarn dev              # 启动开发服务器
yarn build            # 构建生产版本
yarn start            # 启动生产服务器
yarn lint             # 代码检查
yarn lint:fix         # 修复代码问题
yarn type-check       # TypeScript 类型检查
yarn format           # 代码格式化

# 数据库相关
yarn db:generate      # 生成 Prisma 客户端
yarn db:migrate       # 运行数据库迁移
yarn db:push          # 推送 schema 变更 (开发环境)
yarn db:studio        # 打开 Prisma Studio
yarn db:seed          # 数据库种子数据

# 测试相关
yarn test             # 运行测试
yarn test:watch       # 监听模式运行测试
```

## 🐋 Docker 部署

### 开发环境
```bash
# 构建并启动容器
docker-compose up --build

# 后台运行
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止容器
docker-compose down
```

### 生产环境
```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证页面组
│   │   └── login/         # 登录页面
│   ├── (dashboard)/       # 仪表板页面组
│   │   ├── forms/         # 表单管理
│   │   ├── data/          # 数据管理
│   │   ├── settings/      # 系统设置
│   │   └── help/          # 帮助文档
│   ├── api/               # API 路由
│   │   ├── auth/          # 认证 API
│   │   ├── webhook/       # Webhook 接收
│   │   ├── forms/         # 表单配置 API
│   │   ├── data/          # 数据操作 API
│   │   └── users/         # 用户管理 API
│   ├── globals.css        # 全局样式
│   └── layout.tsx         # 根布局
├── components/            # 可复用组件
│   ├── ui/               # 基础 UI 组件
│   ├── forms/            # 表单相关组件
│   ├── data/             # 数据展示组件
│   └── layout/           # 布局组件
├── lib/                  # 工具库
│   ├── auth.ts           # 认证配置
│   ├── db.ts             # 数据库连接
│   ├── logger.ts         # 日志工具
│   └── utils.ts          # 工具函数
├── types/                # TypeScript 类型定义
└── store/                # 状态管理
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `DATABASE_URL` | 数据库连接字符串 | `mysql+pymysql://user:pass@host:port/db` |
| `NEXTAUTH_SECRET` | NextAuth 密钥 | `your-secret-key` |
| `NEXTAUTH_URL` | 应用 URL | `http://localhost:3000` |
| `JWT_SECRET` | JWT 签名密钥 | `your-jwt-secret` |

### 数据库配置

**开发环境 (公网连接)**
```
DATABASE_URL="mysql+pymysql://srmyy_123:<EMAIL>:23387/srmyy_123"
```

**生产环境 (内网连接)**
```
DATABASE_URL="mysql+pymysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"
```

## 📚 API 文档

### 认证接口
- `POST /api/auth/signin` - 用户登录
- `POST /api/auth/signout` - 用户登出
- `GET /api/auth/me` - 获取当前用户

### 表单管理
- `GET /api/forms` - 获取表单列表
- `POST /api/forms` - 创建表单配置
- `PUT /api/forms/[id]` - 更新表单配置
- `DELETE /api/forms/[id]` - 删除表单配置

### 数据管理
- `GET /api/data/[formId]` - 获取表单数据
- `POST /api/data/[formId]` - 创建数据记录
- `PUT /api/data/[formId]/[id]` - 更新数据记录
- `DELETE /api/data/[formId]/[id]` - 删除数据记录
- `GET /api/data/[formId]/export` - 导出数据

### Webhook 接收
- `POST /api/webhook/[formId]` - 接收金数据推送

## 🔒 安全考虑

- 密码使用 bcrypt 加密存储
- API 路由身份验证保护
- 输入数据验证和过滤
- SQL 注入防护 (Prisma ORM)
- XSS 攻击防护
- CSRF 保护

## 📈 性能优化

- Prisma 查询优化
- Next.js 自动代码分割
- 图片优化 (Next.js Image)
- 静态资源缓存
- 数据分页加载

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 联系方式

项目负责人 - [<EMAIL>]

项目链接: [https://github.com/yourusername/lung-function-admin]

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Ant Design](https://ant.design/) - UI 组件库
- [Prisma](https://www.prisma.io/) - 数据库 ORM
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架