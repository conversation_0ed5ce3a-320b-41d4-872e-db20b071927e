# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next/
out/
dist/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Cache directories
.npm
.eslintcache
.prettiercache
*.tsbuildinfo

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Uploads (exclude from build context)
public/uploads/*
!public/uploads/.gitkeep

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Git files
.git
.gitignore

# Documentation files
README*.md
DEVELOPMENT_PLAN.md
PROJECT_DESIGN.md
TECHNICAL_SPEC.md
TENCENT_DEPLOY.md
docs/

# Test files
jest.config.js
jest.setup.js

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Scripts (sensitive)
reset_password.py
reset-password.js
docker-debug.js
test-docker-build.sh

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Lock files (use only yarn.lock)
package-lock.json

# Database files
*.db
*.db-journal

# Other configs
.vercel
.husky/