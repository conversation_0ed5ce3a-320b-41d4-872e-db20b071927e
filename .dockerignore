# 依赖文件
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js构建文件
.next/
out/
dist/
build/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 缓存目录
.npm
.eslintcache
.prettiercache
*.tsbuildinfo

# 上传文件
public/uploads/*
!public/uploads/.gitkeep

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# Git文件
.git
.gitignore

# 文档文件（减少构建上下文）
README*.md
DEVELOPMENT_PLAN.md
PROJECT_DESIGN.md
TECHNICAL_SPEC.md
TENCENT_DEPLOY.md
docs/
产品设计.md

# 测试文件
coverage/
.nyc_output/
jest.config.js
jest.setup.js

# Docker文件
Dockerfile*
docker-compose*
.dockerignore

# 敏感脚本文件
reset_password.py
reset-password.js
docker-debug.js
test-docker-build.sh

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp
*.temp

# Lock文件冲突（统一使用yarn）
package-lock.json

# 数据库文件
*.db
*.db-journal

# Vercel和其他配置
.vercel
.husky/