import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // 如果用户已登录但访问登录页，重定向到仪表板
    if (pathname === '/login' && token) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    // API 路由保护
    if (pathname.startsWith('/api/')) {
      // 排除认证相关的API和Webhook
      const publicApiRoutes = ['/api/auth', '/api/webhook', '/api/health']
      const isPublicRoute = publicApiRoutes.some(route => pathname.startsWith(route))
      
      if (!isPublicRoute && !token) {
        return NextResponse.json(
          { error: 'Unauthorized', message: '请先登录' },
          { status: 401 }
        )
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // 公开路由
        const publicRoutes = ['/login', '/api/auth', '/api/webhook', '/api/health']
        const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))

        if (isPublicRoute) {
          return true
        }

        // 受保护的路由需要token
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - _next/static (静态文件)
     * - _next/image (图像优化文件)
     * - favicon.ico (网站图标)
     * - public 文件夹中的文件
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}