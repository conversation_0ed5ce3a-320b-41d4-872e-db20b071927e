{"permissions": {"allow": ["Bash(ls:*)", "Bash(npm install)", "Bash(sudo chown:*)", "Bash(npm cache clean:*)", "Bash(yarn install)", "<PERSON><PERSON>(mkdir:*)", "Bash(yarn dev)", "<PERSON><PERSON>(pkill:*)", "Bash(yarn db:generate:*)", "Bash(yarn db:migrate:*)", "<PERSON><PERSON>(cat:*)", "Bash(yarn db:push:*)", "Bash(yarn db:seed:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npx prisma:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run build:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "Bash(yarn build:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "Bash(PORT=3001 yarn dev)", "Bash(PORT=3002 yarn dev)", "Bash(npx next dev:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(npm run lint)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(docker build:*)"], "deny": []}}