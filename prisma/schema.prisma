// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id            Int      @id @default(autoincrement())
  username      String   @unique @db.VarChar(50)
  nickname      String?  @db.VarChar(100)
  passwordHash  String   @map("password_hash") @db.VarChar(255)
  email         String?  @db.VarChar(100)
  avatar        String?  @db.VarChar(255)
  role          String   @default("user") @db.VarChar(20)
  isActive      Boolean  @default(true) @map("is_active")
  lastLoginAt   DateTime? @map("last_login_at")
  loginCount    Int      @default(0) @map("login_count")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联关系
  createdFormConfigs FormConfig[] @relation("CreatedBy")
  systemLogs        SystemLog[]

  @@map("users")
  @@index([username])
  @@index([email])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
  @@index([lastLoginAt])
  @@index([role, isActive])
  @@index([isActive, createdAt])
}

// 表单配置表
model FormConfig {
  id           Int      @id @default(autoincrement())
  formId       String   @unique @map("form_id") @db.VarChar(50)
  formName     String   @map("form_name") @db.VarChar(200)
  sampleJson   Json?    @map("sample_json")
  fieldMapping Json     @map("field_mapping")
  fieldCount   Int      @default(0) @map("field_count")
  tableName    String?  @map("table_name") @db.VarChar(100)
  tableCreated Boolean  @default(false) @map("table_created")
  webhookUrl   String?  @map("webhook_url") @db.VarChar(500)
  isActive     Boolean  @default(true) @map("is_active")
  createdById  Int?     @map("created_by")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关联关系
  createdBy User? @relation("CreatedBy", fields: [createdById], references: [id])

  @@map("form_configs")
  @@index([formId])
  @@index([formName])
  @@index([isActive])
  @@index([createdById])
  @@index([createdAt])
  @@index([isActive, createdAt])
  @@index([createdById, isActive])
}

// 系统日志表
model SystemLog {
  id         BigInt   @id @default(autoincrement())
  userId     Int?     @map("user_id")
  action     String   @db.VarChar(50)
  resource   String   @db.VarChar(50)
  resourceId String?  @map("resource_id") @db.VarChar(50)
  details    Json?
  ipAddress  String   @map("ip_address") @db.VarChar(45)
  userAgent  String   @map("user_agent") @db.Text
  createdAt  DateTime @default(now()) @map("created_at")

  // 关联关系
  user User? @relation(fields: [userId], references: [id])

  @@map("system_logs")
  @@index([action])
  @@index([resource])
  @@index([userId])
  @@index([createdAt])
  @@index([action, createdAt])
  @@index([userId, action])
  @@index([resource, action])
}