#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码重置脚本
用于重置肺功能数据管理平台用户密码

注意：此脚本不应纳入git版本管理
"""

import pymysql
import bcrypt
import getpass
import json
from datetime import datetime
import sys
import re

# 数据库配置
DB_CONFIG = {
    'host': 'gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com',
    'port': 23387,
    'user': 'srmyy_123',
    'password': 'gg9gpaEqkg4s',
    'database': 'srmyy_123',
    'charset': 'utf8mb4'
}

print("⚠️  注意：推荐使用 Node.js 版本的脚本 (reset-password.js)")
print("Python版本可能存在bcrypt兼容性问题")

def test_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        connection.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def validate_password(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    if len(password) > 50:
        return False, "密码长度不能超过50位"
    return True, "密码强度符合要求"

def hash_password(password):
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt(rounds=10)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def get_user_by_username(username):
    """根据用户名获取用户信息"""
    connection = pymysql.connect(**DB_CONFIG)
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = "SELECT id, username, nickname, email, role, is_active FROM users WHERE username = %s"
            cursor.execute(sql, (username,))
            return cursor.fetchone()
    finally:
        connection.close()

def list_all_users():
    """列出所有用户"""
    connection = pymysql.connect(**DB_CONFIG)
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT id, username, nickname, email, role, is_active, created_at, last_login_at
            FROM users 
            ORDER BY created_at DESC
            """
            cursor.execute(sql)
            return cursor.fetchall()
    finally:
        connection.close()

def update_user_password(user_id, new_password_hash):
    """更新用户密码"""
    connection = pymysql.connect(**DB_CONFIG)
    try:
        with connection.cursor() as cursor:
            # 更新密码
            sql = "UPDATE users SET password_hash = %s, updated_at = %s WHERE id = %s"
            cursor.execute(sql, (new_password_hash, datetime.now(), user_id))
            
            # 记录系统日志
            log_sql = """
            INSERT INTO system_logs (user_id, action, resource, resource_id, details, ip_address, user_agent, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            details = {
                "action": "password_reset",
                "description": "管理员通过脚本重置用户密码",
                "timestamp": datetime.now().isoformat()
            }
            cursor.execute(log_sql, (
                user_id,
                'password_reset',
                'user',
                str(user_id),
                json.dumps(details, ensure_ascii=False),
                '127.0.0.1',
                'Python Reset Script',
                datetime.now()
            ))
            
            connection.commit()
            return True
    except Exception as e:
        connection.rollback()
        print(f"❌ 更新密码失败: {e}")
        return False
    finally:
        connection.close()

def main():
    """主函数"""
    print("🔐 肺功能数据管理平台 - 密码重置工具")
    print("=" * 50)
    
    # 测试数据库连接
    print("📡 测试数据库连接...")
    if not test_connection():
        print("❌ 无法连接到数据库，请检查网络和配置")
        sys.exit(1)
    print("✅ 数据库连接成功")
    
    while True:
        print("\n请选择操作:")
        print("1. 查看所有用户")
        print("2. 重置用户密码")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            # 查看所有用户
            print("\n📋 用户列表:")
            print("-" * 80)
            users = list_all_users()
            if not users:
                print("❌ 没有找到任何用户")
                continue
                
            print(f"{'ID':<5} {'用户名':<15} {'昵称':<15} {'邮箱':<25} {'角色':<10} {'状态':<6}")
            print("-" * 80)
            for user in users:
                status = "激活" if user['is_active'] else "禁用"
                print(f"{user['id']:<5} {user['username']:<15} {user['nickname'] or '':<15} {user['email'] or '':<25} {user['role']:<10} {status:<6}")
        
        elif choice == '2':
            # 重置用户密码
            print("\n🔄 重置用户密码")
            username = input("请输入用户名: ").strip()
            
            if not username:
                print("❌ 用户名不能为空")
                continue
            
            # 查找用户
            user = get_user_by_username(username)
            if not user:
                print(f"❌ 用户 '{username}' 不存在")
                continue
            
            # 显示用户信息
            print(f"\n📋 用户信息:")
            print(f"ID: {user['id']}")
            print(f"用户名: {user['username']}")
            print(f"昵称: {user['nickname'] or '未设置'}")
            print(f"邮箱: {user['email'] or '未设置'}")
            print(f"角色: {user['role']}")
            print(f"状态: {'激活' if user['is_active'] else '禁用'}")
            
            # 确认操作
            confirm = input(f"\n⚠️  确认要重置用户 '{username}' 的密码吗? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 操作已取消")
                continue
            
            # 输入新密码
            while True:
                new_password = getpass.getpass("请输入新密码: ")
                if not new_password:
                    print("❌ 密码不能为空")
                    continue
                
                is_valid, message = validate_password(new_password)
                if not is_valid:
                    print(f"❌ {message}")
                    continue
                
                # 确认密码
                confirm_password = getpass.getpass("请再次输入新密码: ")
                if new_password != confirm_password:
                    print("❌ 两次输入的密码不一致")
                    continue
                
                break
            
            # 哈希密码
            print("\n🔒 正在加密密码...")
            password_hash = hash_password(new_password)
            
            # 更新密码
            print("💾 正在更新数据库...")
            if update_user_password(user['id'], password_hash):
                print("✅ 密码重置成功!")
                print(f"用户: {username}")
                print(f"新密码: {new_password}")
                print("⚠️  请妥善保管新密码，并提醒用户及时修改")
            else:
                print("❌ 密码重置失败")
        
        elif choice == '3':
            print("\n👋 退出程序")
            break
        
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)